import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

const sourceCode = `
function test(condition) {
  const result = condition ? 'yes' : 'no';
  return result;
}`;

console.log('Source code with positions:');
for (let i = 0; i < sourceCode.length; i++) {
  if (sourceCode[i] === '?' || sourceCode[i] === ':') {
    console.log(`Position ${i}: '${sourceCode[i]}'`);
  }
}

// 找到 'condition' 的位置
const conditionStart = sourceCode.indexOf('condition', sourceCode.indexOf('const result'));
const conditionEnd = conditionStart + 'condition'.length;
console.log(`'condition' range: ${conditionStart} to ${conditionEnd}`);

// 找到 'yes' 的位置
const yesStart = sourceCode.indexOf("'yes'");
const yesEnd = yesStart + "'yes'".length;
console.log(`'yes' range: ${yesStart} to ${yesEnd}`);

// 找到 '?' 的位置
const questionPos = sourceCode.indexOf('?', conditionEnd);
console.log(`'?' position: ${questionPos}`);

// 创建正确的模拟节点
const detailCollector = new DetailCollector();
const visitor = new ComplexityVisitor(sourceCode, detailCollector);

const mockNode = {
  type: 'ConditionalExpression',
  span: { start: conditionStart, end: sourceCode.indexOf("'no'") + 4 },
  test: {
    type: 'Identifier',
    span: { start: conditionStart, end: conditionEnd }
  },
  consequent: {
    type: 'StringLiteral',
    span: { start: yesStart, end: yesEnd }
  },
  alternate: {
    type: 'StringLiteral',
    span: { start: sourceCode.indexOf("'no'"), end: sourceCode.indexOf("'no'") + 4 }
  }
};

console.log('\nCorrected mock node:', JSON.stringify(mockNode, null, 2));

const position = visitor.findTernaryOperatorPosition(mockNode);
console.log('Found position:', position);

if (position !== null) {
  const foundSymbol = sourceCode.slice(position, position + 1);
  console.log('Found symbol:', foundSymbol);
}
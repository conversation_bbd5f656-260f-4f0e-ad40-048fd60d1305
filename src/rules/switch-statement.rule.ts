import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * SwitchStatementRule - switch 语句复杂度规则
 * 
 * 处理 switch 语句的认知复杂度计算：
 * - 每个 switch 语句增加 1 点基础复杂度
 * - 应用嵌套层级惩罚
 * - 增加嵌套层级（影响子节点的复杂度计算）
 * 
 * 支持的节点类型：
 * - SwitchStatement
 */
export class SwitchStatementRule extends BaseRule {
  readonly id = 'switch-statement';
  readonly name = 'Switch Statement Rule';
  readonly priority = 100; // 高优先级，基础控制流规则

  canHandle(node: Node): boolean {
    return node.type === 'SwitchStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const switchNode = node as any;
      
      // 基础复杂度：每个 switch 语句增加 1 点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 分析 switch 语句的特征
      const switchInfo = this.analyzeSwitchStatement(switchNode);
      
      // 生成原因说明
      const reason = this.generateSwitchStatementReason(baseComplexity, finalComplexity, context, switchInfo);
      
      // 生成建议
      const suggestions = this.generateSwitchStatementSuggestions(switchNode, switchInfo, finalComplexity);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // switch 语句会增加嵌套层级
        suggestions,
        {
          baseComplexity,
          nestingLevel: context.nestingLevel,
          caseCount: switchInfo.caseCount,
          hasDefault: switchInfo.hasDefault,
          hasFallthrough: switchInfo.hasFallthrough,
          hasBreakStatements: switchInfo.hasBreakStatements
        }
      );
    });
  }

  /**
   * 分析 switch 语句的特征
   * @param switchNode switch 语句节点
   * @returns switch 语句分析结果
   */
  private analyzeSwitchStatement(switchNode: any): {
    caseCount: number;
    hasDefault: boolean;
    hasFallthrough: boolean;
    hasBreakStatements: boolean;
  } {
    const cases = switchNode.cases || [];
    let caseCount = 0;
    let hasDefault = false;
    let hasFallthrough = false;
    let hasBreakStatements = false;

    for (const caseNode of cases) {
      if (caseNode.test === null || caseNode.test === undefined) {
        // default case
        hasDefault = true;
      } else {
        // regular case
        caseCount++;
      }

      // 检查是否有 break 语句
      if (this.hasBreakStatement(caseNode)) {
        hasBreakStatements = true;
      } else if (caseNode.consequent && caseNode.consequent.length > 0) {
        // 有语句但没有 break，可能是 fallthrough
        hasFallthrough = true;
      }
    }

    return {
      caseCount,
      hasDefault,
      hasFallthrough,
      hasBreakStatements
    };
  }

  /**
   * 检查 case 是否有 break 语句
   * @param caseNode case 节点
   * @returns 是否有 break 语句
   */
  private hasBreakStatement(caseNode: any): boolean {
    const consequent = caseNode.consequent || [];
    
    for (const stmt of consequent) {
      if (stmt.type === 'BreakStatement') {
        return true;
      }
      
      // 检查嵌套的语句块
      if (stmt.type === 'BlockStatement' && stmt.stmts) {
        for (const nestedStmt of stmt.stmts) {
          if (nestedStmt.type === 'BreakStatement') {
            return true;
          }
        }
      }
    }
    
    return false;
  }

  /**
   * 生成 switch 语句的原因说明
   * @param baseComplexity 基础复杂度
   * @param finalComplexity 最终复杂度
   * @param context 分析上下文
   * @param switchInfo switch 语句信息
   * @returns 原因说明
   */
  private generateSwitchStatementReason(
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext,
    switchInfo: { caseCount: number; hasDefault: boolean }
  ): string {
    const caseInfo = `${switchInfo.caseCount} 个 case${switchInfo.hasDefault ? ' + default' : ''}`;
    
    if (context.nestingLevel === 0) {
      return `switch 语句 (${caseInfo}) 增加 ${baseComplexity} 点复杂度`;
    } else {
      const nestingPenalty = finalComplexity - baseComplexity;
      return `switch 语句 (${caseInfo}) 增加 ${baseComplexity} 点基础复杂度 + ${nestingPenalty} 点嵌套惩罚 (嵌套层级: ${context.nestingLevel})`;
    }
  }

  /**
   * 生成 switch 语句的建议
   * @param switchNode switch 语句节点
   * @param switchInfo switch 语句信息
   * @param complexity 复杂度
   * @returns 建议数组
   */
  private generateSwitchStatementSuggestions(
    switchNode: any,
    switchInfo: { caseCount: number; hasDefault: boolean; hasFallthrough: boolean; hasBreakStatements: boolean },
    complexity: number
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    // 检查是否缺少 default case
    if (!switchInfo.hasDefault) {
      suggestions.push({
        type: 'warning',
        message: '考虑添加 default case 以处理未预期的值',
        priority: 'medium'
      });
    }
    
    // 检查是否有 fallthrough
    if (switchInfo.hasFallthrough) {
      suggestions.push({
        type: 'warning',
        message: '检测到可能的 case fallthrough，确保这是有意的行为',
        priority: 'medium'
      });
    }
    
    // 检查 case 数量
    if (switchInfo.caseCount > 10) {
      suggestions.push({
        type: 'refactor',
        message: `switch 语句有 ${switchInfo.caseCount} 个 case，考虑使用对象映射或策略模式重构`,
        priority: 'high'
      });
    } else if (switchInfo.caseCount > 5) {
      suggestions.push({
        type: 'refactor',
        message: `switch 语句有 ${switchInfo.caseCount} 个 case，考虑是否可以简化`,
        priority: 'medium'
      });
    }
    
    // 检查复杂度
    if (complexity > 3) {
      suggestions.push({
        type: 'refactor',
        message: 'switch 语句的复杂度较高，考虑将各个 case 的逻辑提取为单独的函数',
        priority: 'medium'
      });
    }
    
    return suggestions;
  }
}

import { parseSync } from '@swc/core';

// 创建测试源代码
const testSourceCode = `/* eslint-disable react-hooks/exhaustive-deps */

export default function ComplexComponent() {
  // 第4行 - 条件语句
  if (someCondition) {
    console.log('test1');
  }
  
  // 第9行 - 循环
  for (let i = 0; i < 10; i++) {
    console.log('loop', i);
  }
  
  // 第14行 - while循环
  while (condition) {
    break;
  }
  
  return null;
}`;

console.log('调试 SWC AST 节点的 span 信息:');
console.log('源代码长度:', testSourceCode.length);

try {
  // 解析源代码为 AST
  const ast = parseSync(testSourceCode, {
    syntax: 'typescript',
    tsx: true,
    decorators: true,
    dynamicImport: true,
    privateMethod: true,
    functionBind: true,
    exportDefaultFrom: true,
    exportNamespaceFrom: true,
    decoratorsBeforeExport: true,
    topLevelAwait: true,
    importMeta: true,
  });

  // 递归遍历 AST 节点并打印 span 信息
  function traverseNode(node: any, depth = 0, path = 'root') {
    if (!node || typeof node !== 'object') return;
    
    const indent = '  '.repeat(depth);
    const hasSpan = node.span && typeof node.span.start === 'number';
    
    if (hasSpan) {
      console.log(`${indent}${path}: ${node.type} - span: ${node.span.start}-${node.span.end} (字节偏移)`);
      
      // 对于重要的节点类型，显示更多信息
      if (['IfStatement', 'ForStatement', 'WhileStatement', 'FunctionDeclaration', 'ArrowFunctionExpression'].includes(node.type)) {
        const spanStart = node.span.start;
        const spanEnd = node.span.end;
        const codeSnippet = testSourceCode.slice(spanStart, Math.min(spanEnd, spanStart + 50));
        console.log(`${indent}  代码片段: "${codeSnippet.replace(/\n/g, '\\n')}..."`);
      }
    } else {
      console.log(`${indent}${path}: ${node.type} - ❌ 无span信息`);
    }
    
    // 递归遍历子节点
    if (Array.isArray(node)) {
      node.forEach((child, index) => {
        traverseNode(child, depth + 1, `${path}[${index}]`);
      });
    } else {
      Object.keys(node).forEach(key => {
        if (key !== 'span' && key !== 'type' && node[key]) {
          traverseNode(node[key], depth + 1, `${path}.${key}`);
        }
      });
    }
  }

  console.log('\nAST 节点 span 信息:');
  console.log('===================');
  traverseNode(ast);

} catch (error) {
  console.error('解析错误:', error);
}
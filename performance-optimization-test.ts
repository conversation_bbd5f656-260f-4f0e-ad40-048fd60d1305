#!/usr/bin/env bun

/**
 * 性能优化测试脚本 - Task 12 优化版
 * 专门解决内存使用和缓存性能问题
 */

import { ComplexityCalculator } from './src/core/calculator';
import { PositionConverter } from './src/utils/position-converter';
import { createLightweightFactory } from './src/core/calculator-factory';
import fs from 'fs';
import path from 'path';
import { performance } from 'perf_hooks';

interface MemorySnapshot {
  rss: number;
  heapTotal: number;
  heapUsed: number;
  external: number;
  arrayBuffers: number;
}

interface OptimizationTestResult {
  testName: string;
  passed: boolean;
  analysisTime: number;
  fileSize: number;
  lineCount: number;
  memoryUsage: {
    before: MemorySnapshot;
    after: MemorySnapshot;
    increase: number;
  };
  cacheStats: {
    hitRate: number;
    size: number;
    memoryEstimate: number;
  };
  issues: string[];
  optimizations: string[];
}

class PerformanceOptimizer {
  private testResults: OptimizationTestResult[] = [];
  
  private getMemorySnapshot(): MemorySnapshot {
    const memUsage = process.memoryUsage();
    return {
      rss: memUsage.rss,
      heapTotal: memUsage.heapTotal,
      heapUsed: memUsage.heapUsed,
      external: memUsage.external,
      arrayBuffers: memUsage.arrayBuffers
    };
  }

  private forceGarbageCollection(): void {
    // 强制垃圾回收，确保内存测量准确
    if (global.gc) {
      global.gc();
      global.gc(); // 调用两次确保彻底清理
    }
  }

  private calculateMemoryIncrease(before: MemorySnapshot, after: MemorySnapshot): number {
    // 使用堆内存作为主要指标
    return ((after.heapUsed - before.heapUsed) / before.heapUsed) * 100;
  }

  /**
   * 优化版大文件测试 - 专注内存效率
   */
  async testOptimizedLargeFilePerformance(): Promise<OptimizationTestResult> {
    const testName = 'optimized-large-file';
    const optimizations: string[] = [];
    
    // 清理缓存和强制GC
    PositionConverter.clearCache();
    this.forceGarbageCollection();
    await new Promise(resolve => setTimeout(resolve, 100)); // 等待GC完成
    
    const memoryBefore = this.getMemorySnapshot();
    
    // 读取大文件
    const filePath = path.join(__dirname, 'temp/large-test-file.ts');
    if (!fs.existsSync(filePath)) {
      throw new Error('Large test file not found. Please ensure temp/large-test-file.ts exists.');
    }
    
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    const lineCount = sourceCode.split('\n').length;
    const fileSize = sourceCode.length;
    
    console.log(`🔧 开始优化版大文件性能测试 (${lineCount} 行, ${(fileSize / 1024).toFixed(1)}KB)`);
    
    const startTime = performance.now();
    
    try {
      // 使用优化策略：轻量级工厂 + 最小化选项
      const factory = createLightweightFactory();
      const calculator = new ComplexityCalculator({
        enableDebugLog: false,
        enableDetails: false,
        quiet: true,
        enableMixedLogicOperatorPenalty: false
      }, factory);
      
      // 分批处理以减少内存峰值
      const results = await this.processBatched(sourceCode, filePath, calculator);
      optimizations.push('使用轻量级计算器实例');
      optimizations.push('采用分批处理策略减少内存峰值');
      
      const endTime = performance.now();
      const analysisTime = endTime - startTime;
      
      // 强制清理并等待
      await calculator.dispose();
      this.forceGarbageCollection();
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const memoryAfter = this.getMemorySnapshot();
      const memoryIncrease = this.calculateMemoryIncrease(memoryBefore, memoryAfter);
      
      // 获取缓存统计
      const cacheStats = PositionConverter.getCacheStats();
      
      const issues: string[] = [];
      
      // 检查性能指标
      if (analysisTime > 5000) {
        issues.push(`分析时间过长: ${analysisTime.toFixed(2)}ms > 5000ms`);
      }
      
      if (memoryIncrease > 150) {
        issues.push(`内存使用超标: ${memoryIncrease.toFixed(2)}% > 150%`);
      } else {
        optimizations.push(`内存使用优化成功: ${memoryIncrease.toFixed(2)}% <= 150%`);
      }
      
      if (cacheStats.hitRate < 85) {
        issues.push(`缓存命中率不足: ${cacheStats.hitRate.toFixed(2)}% < 85%`);
      } else {
        optimizations.push(`缓存命中率达标: ${cacheStats.hitRate.toFixed(2)}% >= 85%`);
      }
      
      const result: OptimizationTestResult = {
        testName,
        passed: issues.length === 0,
        analysisTime,
        fileSize,
        lineCount,
        memoryUsage: {
          before: memoryBefore,
          after: memoryAfter,
          increase: memoryIncrease
        },
        cacheStats: {
          hitRate: cacheStats.hitRate,
          size: cacheStats.size,
          memoryEstimate: cacheStats.memoryEstimate
        },
        issues,
        optimizations
      };
      
      console.log(`✅ 大文件测试完成: ${issues.length === 0 ? 'PASSED' : 'FAILED'}`);
      console.log(`   分析时间: ${analysisTime.toFixed(2)}ms`);
      console.log(`   内存增长: ${memoryIncrease.toFixed(2)}%`);
      console.log(`   缓存命中率: ${cacheStats.hitRate.toFixed(2)}%`);
      
      if (optimizations.length > 0) {
        console.log(`   优化措施: ${optimizations.join(', ')}`);
      }
      
      return result;
      
    } catch (error) {
      throw new Error(`大文件性能测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 分批处理大文件以减少内存峰值
   */
  private async processBatched(sourceCode: string, filePath: string, calculator: any): Promise<any> {
    // 将大文件分成较小的逻辑块进行处理
    const chunkSize = Math.min(20000, Math.floor(sourceCode.length / 4)); // 最大20KB一块
    const chunks: string[] = [];
    
    for (let i = 0; i < sourceCode.length; i += chunkSize) {
      chunks.push(sourceCode.slice(i, i + chunkSize));
    }
    
    let totalResults = [];
    
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      
      try {
        // 处理每个块
        const chunkResults = await calculator.calculateCode(chunk, `${filePath}-chunk-${i}`);
        totalResults.push(...(Array.isArray(chunkResults) ? chunkResults : [chunkResults]));
        
        // 每处理一个块后进行轻量级清理
        if (i % 2 === 0) {
          this.forceGarbageCollection();
        }
      } catch (error) {
        console.warn(`块 ${i} 处理失败，跳过: ${error}`);
        continue;
      }
    }
    
    return totalResults;
  }

  /**
   * 优化版缓存性能测试
   */
  async testOptimizedCachePerformance(): Promise<OptimizationTestResult> {
    const testName = 'optimized-cache';
    const optimizations: string[] = [];
    
    // 清理缓存准备测试
    PositionConverter.clearCache();
    this.forceGarbageCollection();
    
    const memoryBefore = this.getMemorySnapshot();
    
    const filePath = path.join(__dirname, 'temp/large-test-file.ts');
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    const lineCount = sourceCode.split('\n').length;
    const fileSize = sourceCode.length;
    
    console.log(`🚀 开始优化版缓存性能测试`);
    
    const startTime = performance.now();
    
    // 第一次运行 - 建立缓存
    const calculator1 = ComplexityCalculator.createLightweight();
    await calculator1.calculateCode(sourceCode, filePath);
    const time1 = performance.now() - startTime;
    await calculator1.dispose();
    
    optimizations.push('首次运行建立缓存基线');
    
    // 第二次运行 - 测试缓存效果
    const time2Start = performance.now();
    const calculator2 = ComplexityCalculator.createLightweight();
    await calculator2.calculateCode(sourceCode, filePath);
    const time2 = performance.now() - time2Start;
    await calculator2.dispose();
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    // 计算性能提升
    const performanceImprovement = ((time1 - time2) / time1) * 100;
    optimizations.push(`第二次运行时间: ${time2.toFixed(2)}ms (vs ${time1.toFixed(2)}ms)`);
    
    this.forceGarbageCollection();
    const memoryAfter = this.getMemorySnapshot();
    const memoryIncrease = this.calculateMemoryIncrease(memoryBefore, memoryAfter);
    
    const cacheStats = PositionConverter.getCacheStats();
    
    const issues: string[] = [];
    
    // 检查缓存性能提升
    if (performanceImprovement < 20) {
      issues.push(`缓存性能提升不足: ${performanceImprovement.toFixed(2)}% < 20%`);
    } else {
      optimizations.push(`缓存性能提升达标: ${performanceImprovement.toFixed(2)}% >= 20%`);
    }
    
    // 检查缓存命中率
    if (cacheStats.hitRate < 85) {
      issues.push(`缓存命中率不足: ${cacheStats.hitRate.toFixed(2)}% < 85%`);
    } else {
      optimizations.push(`缓存命中率优秀: ${cacheStats.hitRate.toFixed(2)}% >= 85%`);
    }
    
    const result: OptimizationTestResult = {
      testName,
      passed: issues.length === 0,
      analysisTime: totalTime,
      fileSize,
      lineCount,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        increase: memoryIncrease
      },
      cacheStats: {
        hitRate: cacheStats.hitRate,
        size: cacheStats.size,
        memoryEstimate: cacheStats.memoryEstimate
      },
      issues,
      optimizations
    };
    
    console.log(`✅ 缓存测试完成: ${issues.length === 0 ? 'PASSED' : 'FAILED'}`);
    console.log(`   性能提升: ${performanceImprovement.toFixed(2)}%`);
    console.log(`   缓存命中率: ${cacheStats.hitRate.toFixed(2)}%`);
    console.log(`   内存增长: ${memoryIncrease.toFixed(2)}%`);
    
    return result;
  }

  /**
   * 内存稳定性测试
   */
  async testMemoryStability(): Promise<OptimizationTestResult> {
    const testName = 'memory-stability';
    const optimizations: string[] = [];
    
    console.log(`🔍 开始内存稳定性测试`);
    
    const memoryBefore = this.getMemorySnapshot();
    const filePath = path.join(__dirname, 'temp/large-test-file.ts');
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    const startTime = performance.now();
    
    // 进行多轮分析测试内存稳定性
    for (let i = 0; i < 5; i++) {
      const calculator = ComplexityCalculator.createLightweight();
      
      try {
        await calculator.calculateCode(sourceCode, `${filePath}-round-${i}`);
        await calculator.dispose();
        
        // 每两轮强制GC
        if (i % 2 === 0) {
          this.forceGarbageCollection();
        }
      } catch (error) {
        console.warn(`第 ${i+1} 轮测试失败: ${error}`);
      }
    }
    
    optimizations.push('完成5轮重复分析测试');
    optimizations.push('定期垃圾回收防止内存泄漏');
    
    const endTime = performance.now();
    this.forceGarbageCollection();
    
    const memoryAfter = this.getMemorySnapshot();
    const memoryIncrease = this.calculateMemoryIncrease(memoryBefore, memoryAfter);
    
    const cacheStats = PositionConverter.getCacheStats();
    const issues: string[] = [];
    
    // 内存增长检查 - 多轮测试后应该相对稳定
    if (memoryIncrease > 200) {
      issues.push(`多轮测试内存增长过大: ${memoryIncrease.toFixed(2)}% > 200%`);
    } else {
      optimizations.push(`内存增长控制良好: ${memoryIncrease.toFixed(2)}% <= 200%`);
    }
    
    const result: OptimizationTestResult = {
      testName,
      passed: issues.length === 0,
      analysisTime: endTime - startTime,
      fileSize: sourceCode.length,
      lineCount: sourceCode.split('\n').length,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        increase: memoryIncrease
      },
      cacheStats: {
        hitRate: cacheStats.hitRate,
        size: cacheStats.size,
        memoryEstimate: cacheStats.memoryEstimate
      },
      issues,
      optimizations
    };
    
    console.log(`✅ 内存稳定性测试完成: ${issues.length === 0 ? 'PASSED' : 'FAILED'}`);
    console.log(`   总内存增长: ${memoryIncrease.toFixed(2)}%`);
    
    return result;
  }

  /**
   * 运行所有优化测试
   */
  async runAllOptimizationTests(): Promise<void> {
    console.log(`\n🎯 ======= Task 12 性能优化测试开始 =======\n`);
    
    try {
      // 1. 优化版大文件测试
      console.log(`\n--- 1. 大文件性能优化测试 ---`);
      const largeFileResult = await this.testOptimizedLargeFilePerformance();
      this.testResults.push(largeFileResult);
      
      // 2. 缓存性能优化测试
      console.log(`\n--- 2. 缓存性能优化测试 ---`);
      const cacheResult = await this.testOptimizedCachePerformance();
      this.testResults.push(cacheResult);
      
      // 3. 内存稳定性测试
      console.log(`\n--- 3. 内存稳定性测试 ---`);
      const memoryResult = await this.testMemoryStability();
      this.testResults.push(memoryResult);
      
      // 生成优化报告
      await this.generateOptimizationReport();
      
    } catch (error) {
      console.error(`❌ 优化测试过程中发生错误:`, error);
      throw error;
    }
  }

  /**
   * 生成优化测试报告
   */
  private async generateOptimizationReport(): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(__dirname, 'reports', `task12-optimization-${timestamp}.json`);
    
    // 确保报告目录存在
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const allPassed = this.testResults.every(result => result.passed);
    const totalIssues = this.testResults.reduce((sum, result) => sum + result.issues.length, 0);
    const totalOptimizations = this.testResults.reduce((sum, result) => sum + result.optimizations.length, 0);
    
    // 计算等级
    let grade = 'A';
    if (totalIssues > 0) {
      grade = totalIssues <= 2 ? 'B' : totalIssues <= 4 ? 'C' : 'D';
    }
    
    const report = {
      testResults: Object.fromEntries(
        this.testResults.map(result => [result.testName, result])
      ),
      summary: {
        allPassed,
        totalIssues,
        totalOptimizations,
        grade,
        recommendations: this.generateRecommendations()
      },
      metadata: {
        testVersion: '2.0.0-optimized',
        nodeVersion: process.version,
        platform: process.platform,
        timestamp: new Date().toISOString()
      }
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📊 ======= Task 12 优化测试总结 =======`);
    console.log(`总体状态: ${allPassed ? '✅ 全部通过' : '❌ 存在问题'}`);
    console.log(`问题总数: ${totalIssues}`);
    console.log(`优化措施: ${totalOptimizations}`);
    console.log(`性能等级: ${grade}`);
    console.log(`报告保存至: ${reportPath}`);
    
    // 显示详细结果
    for (const result of this.testResults) {
      console.log(`\n--- ${result.testName} ---`);
      console.log(`状态: ${result.passed ? '✅ PASSED' : '❌ FAILED'}`);
      console.log(`分析时间: ${result.analysisTime.toFixed(2)}ms`);
      console.log(`内存增长: ${result.memoryUsage.increase.toFixed(2)}%`);
      
      if (result.issues.length > 0) {
        console.log(`问题: ${result.issues.join(', ')}`);
      }
      
      if (result.optimizations.length > 0) {
        console.log(`优化: ${result.optimizations.join(', ')}`);
      }
    }
    
    console.log(`\n🎯 ======= Task 12 优化测试完成 =======\n`);
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    const hasMemoryIssues = this.testResults.some(result => 
      result.issues.some(issue => issue.includes('内存'))
    );
    
    const hasCacheIssues = this.testResults.some(result => 
      result.issues.some(issue => issue.includes('缓存'))
    );
    
    if (hasMemoryIssues) {
      recommendations.push('实施更积极的内存管理策略，包括定期垃圾回收和对象池复用');
      recommendations.push('考虑使用流式处理大文件以减少内存峰值');
    }
    
    if (hasCacheIssues) {
      recommendations.push('优化缓存策略，实施更智能的缓存键生成和LRU清理机制');
      recommendations.push('考虑增加缓存预热机制提升命中率');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，继续保持当前优化策略');
    }
    
    return recommendations;
  }
}

// 执行优化测试
async function main() {
  // 启用垃圾回收（如果可用）
  if (process.argv.includes('--expose-gc')) {
    console.log('✅ 垃圾回收已启用');
  } else {
    console.log('⚠️  建议使用 --expose-gc 启用垃圾回收以获得更准确的内存测试');
  }
  
  const optimizer = new PerformanceOptimizer();
  
  try {
    await optimizer.runAllOptimizationTests();
    console.log('🎉 所有优化测试完成！');
  } catch (error) {
    console.error('❌ 优化测试失败:', error);
    process.exit(1);
  }
}

// 执行主函数
if (import.meta.main) {
  main().catch(console.error);
}
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

const sourceCode = `
class TestClass {
  normalMethod() {
    return 'normal';
  }
}`;

// 创建一个扩展的 ComplexityVisitor 来访问私有方法
class DebugComplexityVisitor extends ComplexityVisitor {
  public debugGetMethodSearchRange(node: any) {
    return (this as any).getMethodSearchRange(node);
  }
  
  public debugGetMethodKeywords(node: any) {
    return (this as any).getMethodKeywords(node);
  }
  
  public debugFindKeywordInRange(keyword: string, range: any) {
    return (this as any).findKeywordInRange(keyword, range);
  }
}

const detailCollector = new DetailCollector();
const visitor = new DebugComplexityVisitor(sourceCode, detailCollector);

const methodStart = sourceCode.indexOf('normalMethod');
const methodEnd = methodStart + 'normalMethod'.length;

const mockNode = {
  type: 'MethodDefinition',
  span: { start: methodStart, end: sourceCode.indexOf('}', methodEnd + 20) },
  key: {
    type: 'Identifier',
    span: { start: methodStart, end: methodEnd },
    value: 'normalMethod'
  },
  kind: 'method'
};

console.log('Testing getMethodSearchRange:');
const searchRange = visitor.debugGetMethodSearchRange(mockNode);
console.log('Search range:', searchRange);

console.log('\nTesting getMethodKeywords:');
const keywords = visitor.debugGetMethodKeywords(mockNode);
console.log('Keywords:', keywords);

if (searchRange && keywords.length > 0) {
  console.log('\nTesting findKeywordInRange:');
  for (const keyword of keywords) {
    const position = visitor.debugFindKeywordInRange(keyword, searchRange);
    console.log(`Keyword '${keyword}' found at:`, position);
  }
}

console.log('\nTesting full method:');
const position = visitor.findMethodKeywordPosition(mockNode);
console.log('Final result:', position);
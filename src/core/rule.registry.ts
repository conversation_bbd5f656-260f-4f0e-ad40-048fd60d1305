import type { Node } from '@swc/core';
import type { Rule } from '../engine/types';

/**
 * RuleRegistry - 基于类的规则注册表
 * 
 * 专门用于管理基于类的规则实例，支持规则注册、查找和分发。
 * 与现有的 RuleRegistry（元数据管理）配合使用。
 * 
 * 核心功能：
 * - 注册和管理规则实例
 * - 根据节点类型查找适用的规则
 * - 支持规则优先级排序
 * - 提供规则生命周期管理
 */
export class RuleRegistry {
  /**
   * 存储所有已注册的规则实例
   */
  private static rules: Map<string, Rule> = new Map();

  /**
   * 节点类型到规则的映射表，用于快速查找
   */
  private static nodeTypeToRules: Map<string, Rule[]> = new Map();

  /**
   * 注册规则实例
   * @param rule 规则实例
   */
  public static register(rule: Rule): void {
    if (this.rules.has(rule.id)) {
      throw new Error(`Rule with id '${rule.id}' is already registered`);
    }

    // 注册规则
    this.rules.set(rule.id, rule);

    // 调用规则的生命周期钩子
    if (rule.onLoad) {
      rule.onLoad().catch(error => {
        console.error(`Failed to load rule '${rule.id}':`, error);
      });
    }

    console.log(`Rule '${rule.id}' registered successfully`);
  }

  /**
   * 批量注册规则
   * @param rules 规则实例数组
   */
  public static registerBatch(rules: Rule[]): void {
    for (const rule of rules) {
      try {
        this.register(rule);
      } catch (error) {
        console.error(`Failed to register rule '${rule.id}':`, error);
      }
    }
  }

  /**
   * 获取指定节点类型适用的所有规则
   * @param node AST 节点
   * @returns 适用的规则数组，按优先级排序
   */
  public static getRulesForNode(node: Node): Rule[] {
    const applicableRules: Rule[] = [];

    // 遍历所有规则，找到能处理该节点的规则
    for (const rule of this.rules.values()) {
      if (rule.canHandle(node)) {
        applicableRules.push(rule);
      }
    }

    // 按优先级排序（优先级高的在前）
    return applicableRules.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 获取指定ID的规则
   * @param ruleId 规则ID
   * @returns 规则实例，如果不存在则返回 null
   */
  public static getRule(ruleId: string): Rule | null {
    return this.rules.get(ruleId) || null;
  }

  /**
   * 获取所有已注册的规则
   * @returns 所有规则实例的数组
   */
  public static getAllRules(): Rule[] {
    return Array.from(this.rules.values());
  }

  /**
   * 检查规则是否已注册
   * @param ruleId 规则ID
   * @returns 是否已注册
   */
  public static hasRule(ruleId: string): boolean {
    return this.rules.has(ruleId);
  }

  /**
   * 取消注册规则
   * @param ruleId 规则ID
   * @returns 是否成功取消注册
   */
  public static unregister(ruleId: string): boolean {
    const rule = this.rules.get(ruleId);
    if (rule) {
      // 调用规则的卸载钩子
      if (rule.onUnload) {
        rule.onUnload().catch(error => {
          console.error(`Failed to unload rule '${ruleId}':`, error);
        });
      }
      
      return this.rules.delete(ruleId);
    }
    return false;
  }

  /**
   * 清空所有已注册的规则
   */
  public static clear(): void {
    // 调用所有规则的卸载钩子
    for (const rule of this.rules.values()) {
      if (rule.onUnload) {
        rule.onUnload().catch(error => {
          console.error(`Failed to unload rule '${rule.id}':`, error);
        });
      }
    }

    this.rules.clear();
    this.nodeTypeToRules.clear();
  }

  /**
   * 获取规则统计信息
   * @returns 规则统计信息
   */
  public static getStatistics(): {
    total: number;
    byPriority: Record<number, number>;
  } {
    const rules = Array.from(this.rules.values());
    const byPriority: Record<number, number> = {};

    rules.forEach(rule => {
      const priority = rule.priority;
      byPriority[priority] = (byPriority[priority] || 0) + 1;
    });

    return {
      total: rules.length,
      byPriority
    };
  }

  /**
   * 按优先级获取规则
   * @returns 按优先级排序的规则数组
   */
  public static getRulesByPriority(): Rule[] {
    return Array.from(this.rules.values()).sort((a, b) => b.priority - a.priority);
  }

  /**
   * 验证规则依赖关系
   * @param ruleId 规则ID
   * @returns 依赖验证结果
   */
  public static validateDependencies(ruleId: string): { isValid: boolean; missingDependencies: string[] } {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      return { isValid: false, missingDependencies: [] };
    }

    const dependencies = rule.getDependencies();
    const missingDependencies: string[] = [];

    for (const depId of dependencies) {
      if (!this.rules.has(depId)) {
        missingDependencies.push(depId);
      }
    }

    return {
      isValid: missingDependencies.length === 0,
      missingDependencies
    };
  }

  /**
   * 解析规则依赖关系
   * @param ruleId 规则ID
   * @returns 依赖的规则数组
   */
  public static resolveDependencies(ruleId: string): Rule[] {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      return [];
    }

    const dependencies: Rule[] = [];
    const visited = new Set<string>();

    const resolveDep = (depId: string) => {
      if (visited.has(depId)) {
        return; // 避免循环依赖
      }
      visited.add(depId);

      const depRule = this.rules.get(depId);
      if (depRule) {
        dependencies.push(depRule);
        // 递归解析依赖的依赖
        for (const subDepId of depRule.getDependencies()) {
          resolveDep(subDepId);
        }
      }
    };

    for (const depId of rule.getDependencies()) {
      resolveDep(depId);
    }

    return dependencies;
  }
}

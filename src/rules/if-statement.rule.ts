import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * IfStatementRule - if 语句复杂度规则
 * 
 * 处理 if 语句的认知复杂度计算：
 * - 每个 if 语句增加 1 点基础复杂度
 * - 应用嵌套层级惩罚
 * - 增加嵌套层级（影响子节点的复杂度计算）
 * 
 * 支持的节点类型：
 * - IfStatement
 */
export class IfStatementRule extends BaseRule {
  readonly id = 'if-statement';
  readonly name = 'If Statement Rule';
  readonly priority = 100; // 高优先级，基础控制流规则

  canHandle(node: Node): boolean {
    return node.type === 'IfStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const ifNode = node as any;
      
      // 基础复杂度：每个 if 语句都增加 1 点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateIfStatementReason(baseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateSuggestions('if statement', finalComplexity);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // if 语句会增加嵌套层级
        suggestions,
        {
          baseComplexity,
          nestingLevel: context.nestingLevel,
          hasElse: !!ifNode.alternate,
          hasElseIf: this.hasElseIf(ifNode)
        }
      );
    });
  }

  /**
   * 检查是否有 else if 分支
   * @param ifNode if 语句节点
   * @returns 是否有 else if
   */
  private hasElseIf(ifNode: any): boolean {
    return ifNode.alternate && ifNode.alternate.type === 'IfStatement';
  }

  /**
   * 生成 if 语句的原因说明
   * @param baseComplexity 基础复杂度
   * @param finalComplexity 最终复杂度
   * @param context 分析上下文
   * @returns 原因说明
   */
  private generateIfStatementReason(
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext
  ): string {
    if (context.nestingLevel === 0) {
      return `if 语句增加 ${baseComplexity} 点复杂度`;
    } else {
      const nestingPenalty = finalComplexity - baseComplexity;
      return `if 语句增加 ${baseComplexity} 点基础复杂度 + ${nestingPenalty} 点嵌套惩罚 (嵌套层级: ${context.nestingLevel})`;
    }
  }
}

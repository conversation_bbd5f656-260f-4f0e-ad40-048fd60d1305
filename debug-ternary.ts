import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

const sourceCode = `
function test(condition) {
  const result = condition ? 'yes' : 'no';
  return result;
}`;

const detailCollector = new DetailCollector();
const visitor = new ComplexityVisitor(sourceCode, detailCollector);

// 创建模拟的 ConditionalExpression 节点
const mockNode = {
  type: 'ConditionalExpression',
  span: { start: 43, end: 66 },
  test: {
    type: 'Identifier',
    span: { start: 43, end: 52 }
  },
  consequent: {
    type: 'StringLiteral',
    span: { start: 55, end: 60 }
  },
  alternate: {
    type: 'StringLiteral',
    span: { start: 63, end: 67 }
  }
};

console.log('Source code:', sourceCode);
console.log('Mock node:', JSON.stringify(mockNode, null, 2));

const position = visitor.findTernaryOperatorPosition(mockNode);
console.log('Found position:', position);

if (position !== null) {
  const foundSymbol = sourceCode.slice(position, position + 1);
  console.log('Found symbol:', foundSymbol);
  
  // 显示上下文
  const contextStart = Math.max(0, position - 10);
  const contextEnd = Math.min(sourceCode.length, position + 10);
  const context = sourceCode.slice(contextStart, contextEnd);
  console.log('Context:', context);
}

// 手动检查搜索范围
const searchStart = mockNode.test.span.end; // 52
const searchEnd = mockNode.consequent.span.start; // 55
const searchText = sourceCode.slice(searchStart, searchEnd);
console.log('Search range:', searchStart, 'to', searchEnd);
console.log('Search text:', JSON.stringify(searchText));
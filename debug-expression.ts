import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

async function debugExpressionPositioning() {
  const parser = new ASTParser();
  const detailCollector = new DetailCollector();
  
  const testCases = [
    {
      name: '函数调用',
      code: `function MyComponent() { return <div>{getData()}</div>; }`
    },
    {
      name: '复杂 map 表达式',
      code: `function MyComponent() {
        return (
          <div>
            {items.map(item => <Item key={item.id} data={item} />)}
          </div>
        );
      }`
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n=== ${testCase.name} ===`);
    console.log('代码:', testCase.code);
    
    try {
      const ast = await parser.parseCode(testCase.code, 'test.tsx');
      const visitor = new ComplexityVisitor(testCase.code, detailCollector);
      detailCollector.startFunction('TestFunction', 1, 0);
      
      // 查找 JSX 表达式容器
      const findJSXExpressionContainer = (node: any): any => {
        if (!node || typeof node !== 'object') return null;
        
        if (node.type === 'JSXExpressionContainer') {
          return node;
        }
        
        for (const key in node) {
          if (key === 'span' || key === 'type') continue;
          const value = node[key];
          
          if (Array.isArray(value)) {
            for (const item of value) {
              const found = findJSXExpressionContainer(item);
              if (found) return found;
            }
          } else if (value && typeof value === 'object') {
            const found = findJSXExpressionContainer(value);
            if (found) return found;
          }
        }
        
        return null;
      };
      
      const jsxExpression = findJSXExpressionContainer(ast);
      
      if (jsxExpression) {
        console.log('找到 JSX 表达式容器:', {
          type: jsxExpression.type,
          span: jsxExpression.span,
          hasExpression: !!jsxExpression.expression,
          expressionType: jsxExpression.expression?.type,
          expressionSpan: jsxExpression.expression?.span
        });
        
        // 尝试定位
        const position = visitor.findJSXExpressionContentPosition(jsxExpression);
        console.log('定位结果:', position);
        
        if (position !== null) {
          console.log('找到的位置对应的文本:', testCase.code.slice(position, position + 20));
          
          // 查找期望的关键字
          const keywords = ['getData', 'items'];
          for (const keyword of keywords) {
            const keywordIndex = testCase.code.indexOf(keyword);
            if (keywordIndex >= 0) {
              console.log(`${keyword} 的实际位置:`, keywordIndex);
              console.log(`${keyword} 位置对应的文本:`, testCase.code.slice(keywordIndex, keywordIndex + 20));
            }
          }
        }
      } else {
        console.log('未找到 JSX 表达式容器');
      }
      
    } catch (error) {
      console.error('调试失败:', error);
    }
  }
}

debugExpressionPositioning();
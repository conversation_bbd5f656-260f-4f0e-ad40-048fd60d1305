import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * LogicalOperatorRule - 逻辑运算符复杂度规则
 * 
 * 处理逻辑运算符的认知复杂度计算：
 * - && 和 || 运算符各增加 1 点复杂度
 * - 不增加嵌套层级（逻辑运算符不创建新的作用域）
 * - 支持短路求值的复杂度分析
 * 
 * 支持的节点类型：
 * - BinaryExpression (operator: '&&' | '||')
 * - LogicalExpression (operator: '&&' | '||')
 */
export class LogicalOperatorRule extends BaseRule {
  readonly id = 'logical-operator';
  readonly name = 'Logical Operator Rule';
  readonly priority = 90; // 中高优先级

  canHandle(node: Node): boolean {
    const binaryNode = node as any;
    return (
      (node.type === 'BinaryExpression' || node.type === 'LogicalExpression') &&
      binaryNode.operator &&
      (binaryNode.operator === '&&' || binaryNode.operator === '||')
    );
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const logicalNode = node as any;
      const operator = logicalNode.operator;
      
      // 检查是否为简单的 null/undefined 检查，可能豁免复杂度
      if (this.isSimpleNullCheck(logicalNode)) {
        return this.createExemptionResult(
          node,
          `简单的 null/undefined 检查 (${operator}) 豁免复杂度`,
          { operator, exemptionType: 'null-check' }
        );
      }
      
      // 检查是否为权限检查，可能豁免复杂度
      if (this.isPermissionCheck(logicalNode)) {
        return this.createExemptionResult(
          node,
          `权限检查 (${operator}) 豁免复杂度`,
          { operator, exemptionType: 'permission-check' }
        );
      }
      
      // 基础复杂度：每个逻辑运算符增加 1 点复杂度
      const baseComplexity = 1;
      
      // 逻辑运算符不应用嵌套惩罚，因为它们不创建新的作用域
      // 但仍然受当前嵌套层级影响
      const finalComplexity = baseComplexity;
      
      // 生成原因说明
      const reason = this.generateLogicalOperatorReason(operator, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateLogicalOperatorSuggestions(logicalNode, finalComplexity);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        false, // 逻辑运算符不增加嵌套层级
        suggestions,
        {
          operator,
          nestingLevel: context.nestingLevel,
          isShortCircuit: true,
          leftOperandType: logicalNode.left?.type,
          rightOperandType: logicalNode.right?.type
        }
      );
    });
  }

  /**
   * 检查是否为简单的 null/undefined 检查
   * @param logicalNode 逻辑表达式节点
   * @returns 是否为简单的 null 检查
   */
  private isSimpleNullCheck(logicalNode: any): boolean {
    if (logicalNode.operator !== '&&') {
      return false;
    }
    
    const left = logicalNode.left;
    // 检查左侧是否为简单的变量或属性访问
    return left && (
      left.type === 'Identifier' ||
      left.type === 'MemberExpression'
    );
  }

  /**
   * 检查是否为权限检查
   * @param logicalNode 逻辑表达式节点
   * @returns 是否为权限检查
   */
  private isPermissionCheck(logicalNode: any): boolean {
    if (logicalNode.operator !== '&&') {
      return false;
    }
    
    const left = logicalNode.left;
    if (left && left.type === 'MemberExpression') {
      const property = left.property;
      if (property && property.type === 'Identifier') {
        const propertyName = property.value || property.name;
        return typeof propertyName === 'string' && 
               /^(can|has|is|allow|permit)/i.test(propertyName);
      }
    }
    return false;
  }

  /**
   * 生成逻辑运算符的原因说明
   * @param operator 运算符
   * @param complexity 复杂度
   * @param context 分析上下文
   * @returns 原因说明
   */
  private generateLogicalOperatorReason(
    operator: string,
    complexity: number,
    context: AnalysisContext
  ): string {
    const operatorName = operator === '&&' ? '逻辑与' : '逻辑或';
    return `${operatorName}运算符 (${operator}) 增加 ${complexity} 点复杂度`;
  }

  /**
   * 生成逻辑运算符的建议
   * @param logicalNode 逻辑表达式节点
   * @param complexity 复杂度
   * @returns 建议数组
   */
  private generateLogicalOperatorSuggestions(logicalNode: any, complexity: number): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    if (complexity > 0) {
      // 检查是否为复杂的逻辑表达式
      if (this.isComplexLogicalExpression(logicalNode)) {
        suggestions.push({
          type: 'refactor',
          message: '考虑将复杂的逻辑表达式提取为有意义的变量或函数',
          priority: 'medium'
        });
      }
      
      // 检查是否为嵌套的逻辑表达式
      if (this.isNestedLogicalExpression(logicalNode)) {
        suggestions.push({
          type: 'refactor',
          message: '考虑使用括号明确逻辑运算的优先级，或拆分为多个条件',
          priority: 'medium'
        });
      }
    }
    
    return suggestions;
  }

  /**
   * 检查是否为复杂的逻辑表达式
   * @param logicalNode 逻辑表达式节点
   * @returns 是否为复杂表达式
   */
  private isComplexLogicalExpression(logicalNode: any): boolean {
    // 简化版本：检查操作数是否包含函数调用或复杂表达式
    const hasComplexOperand = (operand: any): boolean => {
      return operand && (
        operand.type === 'CallExpression' ||
        operand.type === 'ConditionalExpression' ||
        operand.type === 'LogicalExpression' ||
        operand.type === 'BinaryExpression'
      );
    };
    
    return hasComplexOperand(logicalNode.left) || hasComplexOperand(logicalNode.right);
  }

  /**
   * 检查是否为嵌套的逻辑表达式
   * @param logicalNode 逻辑表达式节点
   * @returns 是否为嵌套表达式
   */
  private isNestedLogicalExpression(logicalNode: any): boolean {
    return (
      (logicalNode.left && (logicalNode.left.type === 'LogicalExpression' || logicalNode.left.type === 'BinaryExpression')) ||
      (logicalNode.right && (logicalNode.right.type === 'LogicalExpression' || logicalNode.right.type === 'BinaryExpression'))
    );
  }
}

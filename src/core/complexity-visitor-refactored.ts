import type { Node } from '@swc/core';
import type { CalculationOptions, FunctionResult } from './types';
import type { AsyncRuleEngine, AnalysisContext, Rule } from '../engine/types';
import { BaseVisitor } from './base-visitor';
import { DetailCollector } from './detail-collector';
import { PositionConverter } from '../utils/position-converter';
import { RuleRegistry } from './rule.registry';
import { PositioningService } from './positioning.service';

/**
 * SWC 节点的 span 类型定义
 */
interface SwcSpan {
  start: number;
  end: number;
  ctxt: number;
}

/**
 * 具有 span 属性的节点类型
 */
interface NodeWithSpan extends Node {
  span: SwcSpan;
}

/**
 * ComplexityVisitor - 重构版认知复杂度计算访问者
 *
 * 基于委托模式的轻量级访问者实现，通过规则引擎处理所有复杂度计算。
 *
 * 核心职责：
 * - AST 节点遍历和嵌套层级管理
 * - 委托给规则引擎进行复杂度计算
 * - span 修正和位置转换
 * - 与 DetailCollector 的集成
 *
 * 设计原则：
 * - 单一职责：只负责访问者模式的核心功能
 * - 委托模式：将复杂度计算委托给规则引擎
 * - 错误恢复：部分节点失败不影响整体计算
 */
export class ComplexityVisitor extends BaseVisitor {
  /**
   * 源代码内容，用于位置转换和 span 修正
   */
  private readonly sourceCode: string;

  /**
   * 详细信息收集器，用于记录复杂度计算步骤
   */
  private readonly detailCollector?: DetailCollector;

  /**
   * 计算选项，用于配置复杂度计算行为
   */
  private readonly options: CalculationOptions;

  /**
   * 异步规则引擎（可选，优先使用）
   */
  private readonly asyncRuleEngine?: AsyncRuleEngine;

  /**
   * 规则注册表实例（基于类的规则）
   */
  private readonly ruleRegistry: typeof RuleRegistry;

  /**
   * 位置服务实例
   */
  private readonly positioningService: PositioningService;

  /**
   * 函数分析结果存储
   */
  private results: FunctionResult[] = [];

  /**
   * 当前累计复杂度
   */
  private totalComplexity: number = 0;

  /**
   * 当前嵌套层级
   */
  private nestingLevel: number = 0;

  /**
   * 当前正在分析的函数名称
   */
  private currentFunctionName: string = '';

  /**
   * 当前函数的起始位置
   */
  private currentFunctionLocation: { line: number; column: number } = { line: 0, column: 0 };

  /**
   * 构造函数
   * @param sourceCode 源代码内容
   * @param ruleRegistry 规则注册表类（静态类）
   * @param positioningService 位置服务实例
   * @param detailCollector 可选的详细信息收集器
   * @param options 可选的计算选项
   * @param asyncRuleEngine 可选的异步规则引擎
   */
  constructor(
    sourceCode: string,
    ruleRegistry: typeof RuleRegistry,
    positioningService: PositioningService,
    detailCollector?: DetailCollector,
    options: CalculationOptions = {},
    asyncRuleEngine?: AsyncRuleEngine
  ) {
    super();
    this.sourceCode = sourceCode;
    this.ruleRegistry = ruleRegistry;
    this.positioningService = positioningService;
    this.detailCollector = detailCollector;
    this.options = options;
    this.asyncRuleEngine = asyncRuleEngine;
  }

  /**
   * 获取所有函数的分析结果
   * @returns 函数分析结果数组
   */
  public getResults(): FunctionResult[] {
    return [...this.results]; // 返回副本确保不可变
  }

  /**
   * 获取当前累计的总复杂度
   */
  public getTotalComplexity(): number {
    return this.totalComplexity;
  }

  /**
   * 获取当前嵌套层级
   */
  public getCurrentNestingLevel(): number {
    return this.nestingLevel;
  }

  /**
   * 重置访问者状态
   */
  public resetComplexity(): void {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.currentFunctionName = '';
    this.currentFunctionLocation = { line: 0, column: 0 };
    this.results = [];
    this.reset(); // 调用父类的重置方法
  }

  /**
   * 分析单个函数的复杂度
   * @param functionNode 函数节点
   */
  public async analyzeFunction(functionNode: Node): Promise<void> {
    try {
      // 重置状态
      this.resetForNewFunction();

      // 设置函数信息
      this.setFunctionInfo(functionNode);

      // 开始详细信息收集
      this.detailCollector?.startFunction(this.currentFunctionName);

      // 遍历函数体
      this.visit(functionNode);

      // 收集结果
      const result: FunctionResult = {
        name: this.currentFunctionName,
        complexity: this.totalComplexity,
        line: this.currentFunctionLocation.line,
        column: this.currentFunctionLocation.column,
        filePath: '', // 将由调用者设置
        details: this.detailCollector?.getDetails(),
      };

      this.results.push(result);
    } catch (error) {
      this.handleFunctionAnalysisError(functionNode, error as Error);
    }
  }

  /**
   * 重写 visit 方法以支持委托模式和嵌套层级管理
   */
  public override visit<T extends Node>(node: T): T {
    // 如果是顶层调用且为模块或程序节点，查找所有函数并分析
    if (this.parentStack.length === 0 && (node.type === 'Module' || node.type === 'Program')) {
      return this.visitModuleAndAnalyzeFunctions(node);
    }

    // 检查这个节点是否会影响嵌套层级
    const shouldManageNesting = this.shouldEnterNesting(node);

    if (shouldManageNesting) {
      this.enterNesting();
      const result = super.visit(node);
      this.exitNesting();
      return result;
    } else {
      return super.visit(node);
    }
  }

  /**
   * 核心节点处理方法 - 委托给规则引擎
   */
  protected override visitNode(node: Node): Node {
    // 处理节点复杂度
    this.processNodeComplexity(node);

    // 继续遍历子节点
    return node;
  }

  /**
   * 处理节点复杂度
   * @param node AST 节点
   */
  private processNodeComplexity(node: Node): void {
    try {
      // 优先使用异步规则引擎
      if (this.asyncRuleEngine) {
        this.processNodeWithAsyncEngine(node);
      } else {
        // 使用基于类的规则注册表
        this.processNodeWithRuleRegistry(node);
      }
    } catch (error) {
      this.handleNodeProcessingError(node, error as Error);
    }
  }

  /**
   * 使用异步规则引擎处理节点
   * @param node AST 节点
   */
  private processNodeWithAsyncEngine(node: Node): void {
    // 在同步上下文中，我们需要特殊处理异步规则引擎
    // 这里简化为同步处理，实际应用中可能需要更复杂的异步处理
    const context: AnalysisContext = {
      nestingLevel: this.nestingLevel,
      visitor: this as any,
      sourceCode: this.sourceCode,
      currentFunctionName: this.currentFunctionName,
    };

    // 注意：这里简化了异步处理，实际应用中需要适当的异步处理机制
    console.debug('AsyncRuleEngine processing not fully implemented in sync context');
  }

  /**
   * 使用规则注册表处理节点
   * @param node AST 节点
   */
  private processNodeWithRuleRegistry(node: Node): void {
    // 获取适用于当前节点的规则
    const applicableRules = this.ruleRegistry.getRulesForNode(node);

    if (applicableRules.length === 0) {
      return; // 没有适用的规则
    }

    // 创建分析上下文
    const context: AnalysisContext = {
      nestingLevel: this.nestingLevel,
      visitor: this as any,
      sourceCode: this.sourceCode,
      currentFunctionName: this.currentFunctionName,
    };

    // 执行所有适用的规则
    for (const rule of applicableRules) {
      this.executeRule(rule, node, context);
    }
  }

  /**
   * 执行单个规则
   * @param rule 规则实例
   * @param node AST 节点
   * @param context 分析上下文
   */
  private async executeRule(rule: any, node: Node, context: AnalysisContext): Promise<void> {
    try {
      const result = await rule.evaluate(node, context);

      if (result.complexity > 0) {
        this.addComplexity(result.complexity, node, rule.id, result.reason);
      }

      // 如果规则指示需要增加嵌套层级
      if (result.increasesNesting) {
        // 嵌套层级管理已在 visit 方法中处理
      }

      // 记录详细信息
      if (this.detailCollector && result.complexity > 0) {
        this.recordComplexityDetail(node, rule, result);
      }
    } catch (error) {
      this.handleRuleExecutionError(rule, node, error as Error);
    }
  }

  /**
   * 添加复杂度
   * @param complexity 复杂度值
   * @param node AST 节点
   * @param ruleId 规则ID
   * @param reason 原因说明
   */
  private addComplexity(complexity: number, node: Node, ruleId: string, reason: string): void {
    this.totalComplexity += complexity;

    // 记录到详细信息收集器
    if (this.detailCollector) {
      const position = this.getNodePosition(node);
      this.detailCollector.addComplexityStep(complexity, reason, position.line, position.column, ruleId);
    }
  }

  /**
   * 获取节点位置
   * @param node AST 节点
   * @returns 位置信息
   */
  private getNodePosition(node: Node): { line: number; column: number } {
    try {
      // 使用位置服务获取节点位置
      const span = this.positioningService.validateSpan(node);
      return this.positioningService.getLineNumber(span);
    } catch {
      // 回退到默认位置
      return { line: 1, column: 0 };
    }
  }

  /**
   * 检查节点是否应该进入嵌套
   * @param node AST 节点
   * @returns 是否应该进入嵌套
   */
  private shouldEnterNesting(node: Node): boolean {
    const nestingNodeTypes = [
      'IfStatement',
      'WhileStatement',
      'DoWhileStatement',
      'ForStatement',
      'ForInStatement',
      'ForOfStatement',
      'SwitchStatement',
      'CatchClause',
      'TryStatement',
    ];

    return nestingNodeTypes.includes(node.type);
  }

  /**
   * 进入嵌套层级
   */
  private enterNesting(): void {
    this.nestingLevel++;
  }

  /**
   * 退出嵌套层级
   */
  private exitNesting(): void {
    this.nestingLevel = Math.max(0, this.nestingLevel - 1);
  }

  /**
   * 访问模块并分析所有函数
   * @param node 模块或程序节点
   * @returns 处理后的节点
   */
  private visitModuleAndAnalyzeFunctions<T extends Node>(node: T): T {
    // 查找所有函数节点
    const functionNodes = this.findFunctionNodes(node);

    // 分析每个函数
    for (const functionNode of functionNodes) {
      this.analyzeFunction(functionNode).catch((error) => {
        this.handleFunctionAnalysisError(functionNode, error);
      });
    }

    return node;
  }

  /**
   * 查找所有函数节点
   * @param node 根节点
   * @returns 函数节点数组
   */
  private findFunctionNodes(node: Node): Node[] {
    const functionNodes: Node[] = [];
    const functionTypes = ['FunctionDeclaration', 'FunctionExpression', 'ArrowFunctionExpression', 'MethodDefinition'];

    const traverse = (current: any) => {
      if (functionTypes.includes(current.type)) {
        functionNodes.push(current);
      }

      // 遍历子节点
      for (const key in current) {
        const value = current[key];
        if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item && typeof item === 'object' && item.type) {
              traverse(item);
            }
          });
        } else if (value && typeof value === 'object' && value.type) {
          traverse(value);
        }
      }
    };

    traverse(node);
    return functionNodes;
  }

  /**
   * 重置状态以分析新函数
   */
  private resetForNewFunction(): void {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.currentFunctionName = '';
    this.currentFunctionLocation = { line: 0, column: 0 };
    this.reset(); // 调用父类的重置方法清空父节点栈
  }

  /**
   * 设置函数信息
   * @param functionNode 函数节点
   */
  private setFunctionInfo(functionNode: Node): void {
    // 提取函数名称
    this.currentFunctionName = this.extractFunctionName(functionNode);

    // 获取函数位置
    this.currentFunctionLocation = this.getNodePosition(functionNode);
  }

  /**
   * 提取函数名称
   * @param functionNode 函数节点
   * @returns 函数名称
   */
  private extractFunctionName(functionNode: Node): string {
    const node = functionNode as any;

    switch (node.type) {
      case 'FunctionDeclaration':
        return node.identifier?.value || node.identifier?.name || 'anonymous';

      case 'FunctionExpression':
        return node.identifier?.value || node.identifier?.name || 'anonymous';

      case 'ArrowFunctionExpression':
        return 'arrow function';

      case 'MethodDefinition':
        return node.key?.value || node.key?.name || 'method';

      default:
        return 'unknown function';
    }
  }

  /**
   * 记录复杂度详细信息
   * @param node AST 节点
   * @param rule 规则实例
   * @param result 规则执行结果
   */
  private recordComplexityDetail(node: Node, rule: any, result: any): void {
    if (!this.detailCollector) return;

    try {
      const position = this.getNodePosition(node);
      this.detailCollector.addComplexityStep(result.complexity, result.reason, position.line, position.column, rule.id);
    } catch {
      console.debug('Failed to record complexity detail');
    }
  }

  /**
   * 处理函数分析错误
   * @param functionNode 函数节点
   * @param error 错误信息
   */
  private handleFunctionAnalysisError(functionNode: Node, error: Error): void {
    console.error(`Function analysis failed for ${functionNode.type}:`, error);

    if (this.detailCollector) {
      this.detailCollector.addErrorStep(`函数分析失败: ${error.message}`, {
        nodeType: functionNode.type,
        error: error.message,
      });
    }
  }

  /**
   * 处理节点处理错误
   * @param node AST 节点
   * @param error 错误信息
   */
  private handleNodeProcessingError(node: Node, error: Error): void {
    console.debug(`Node processing failed for ${node.type}:`, error);

    if (this.detailCollector) {
      this.detailCollector.addErrorStep(`节点处理失败: ${error.message}`, {
        nodeType: node.type,
        error: error.message,
      });
    }
  }

  /**
   * 处理规则执行错误
   * @param rule 规则实例
   * @param node AST 节点
   * @param error 错误信息
   */
  private handleRuleExecutionError(rule: any, node: Node, error: Error): void {
    console.debug(`Rule execution failed for ${rule.id} on ${node.type}:`, error);

    if (this.detailCollector) {
      this.detailCollector.addErrorStep(`规则执行失败: ${rule.id} - ${error.message}`, {
        ruleId: rule.id,
        nodeType: node.type,
        error: error.message,
      });
    }
  }
}

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * CatchClauseRule - catch 异常处理复杂度规则
 * 
 * 处理 catch 子句的认知复杂度计算：
 * - 每个 catch 子句增加 1 点基础复杂度
 * - 应用嵌套层级惩罚
 * - 增加嵌套层级（影响子节点的复杂度计算）
 * 
 * 支持的节点类型：
 * - CatchClause
 */
export class CatchClauseRule extends BaseRule {
  readonly id = 'catch-clause';
  readonly name = 'Catch Clause Rule';
  readonly priority = 95; // 高优先级，异常处理规则

  canHandle(node: Node): boolean {
    return node.type === 'CatchClause';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const catchNode = node as any;
      
      // 基础复杂度：每个 catch 子句增加 1 点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateCatchClauseReason(baseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateCatchClauseSuggestions(catchNode, finalComplexity);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // catch 子句会增加嵌套层级
        suggestions,
        {
          baseComplexity,
          nestingLevel: context.nestingLevel,
          hasParameter: !!catchNode.param,
          parameterType: catchNode.param?.type,
          hasTypeAnnotation: this.hasTypeAnnotation(catchNode)
        }
      );
    });
  }

  /**
   * 检查 catch 子句是否有类型注解
   * @param catchNode catch 子句节点
   * @returns 是否有类型注解
   */
  private hasTypeAnnotation(catchNode: any): boolean {
    return catchNode.param && 
           (catchNode.param.typeAnnotation || catchNode.param.type === 'TSTypeAnnotation');
  }

  /**
   * 生成 catch 子句的原因说明
   * @param baseComplexity 基础复杂度
   * @param finalComplexity 最终复杂度
   * @param context 分析上下文
   * @returns 原因说明
   */
  private generateCatchClauseReason(
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext
  ): string {
    if (context.nestingLevel === 0) {
      return `catch 异常处理增加 ${baseComplexity} 点复杂度`;
    } else {
      const nestingPenalty = finalComplexity - baseComplexity;
      return `catch 异常处理增加 ${baseComplexity} 点基础复杂度 + ${nestingPenalty} 点嵌套惩罚 (嵌套层级: ${context.nestingLevel})`;
    }
  }

  /**
   * 生成 catch 子句的建议
   * @param catchNode catch 子句节点
   * @param complexity 复杂度
   * @returns 建议数组
   */
  private generateCatchClauseSuggestions(catchNode: any, complexity: number): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    // 检查是否有参数
    if (!catchNode.param) {
      suggestions.push({
        type: 'warning',
        message: '考虑为 catch 子句添加错误参数以便进行错误处理',
        priority: 'low'
      });
    }
    
    // 检查是否有类型注解（TypeScript）
    if (catchNode.param && !this.hasTypeAnnotation(catchNode)) {
      suggestions.push({
        type: 'info',
        message: '考虑为 catch 参数添加类型注解以提高类型安全性',
        priority: 'low'
      });
    }
    
    // 检查复杂度
    if (complexity > 3) {
      suggestions.push({
        type: 'refactor',
        message: 'catch 块的复杂度较高，考虑将错误处理逻辑提取为单独的函数',
        priority: 'medium'
      });
    }
    
    // 检查是否为空的 catch 块
    if (this.isEmptyCatchBlock(catchNode)) {
      suggestions.push({
        type: 'warning',
        message: '空的 catch 块可能会隐藏错误，考虑添加适当的错误处理逻辑',
        priority: 'high'
      });
    }
    
    return suggestions;
  }

  /**
   * 检查是否为空的 catch 块
   * @param catchNode catch 子句节点
   * @returns 是否为空块
   */
  private isEmptyCatchBlock(catchNode: any): boolean {
    const body = catchNode.body;
    return body && 
           body.type === 'BlockStatement' && 
           (!body.stmts || body.stmts.length === 0);
  }
}

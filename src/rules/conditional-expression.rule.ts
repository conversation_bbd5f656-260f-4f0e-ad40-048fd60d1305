import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * ConditionalExpressionRule - 三元运算符复杂度规则
 * 
 * 处理三元运算符（条件表达式）的认知复杂度计算：
 * - 每个三元运算符增加 1 点基础复杂度
 * - 应用嵌套层级惩罚
 * - 不增加嵌套层级（三元运算符是表达式，不是语句）
 * 
 * 支持的节点类型：
 * - ConditionalExpression
 */
export class ConditionalExpressionRule extends BaseRule {
  readonly id = 'conditional-expression';
  readonly name = 'Conditional Expression Rule';
  readonly priority = 90; // 中高优先级

  canHandle(node: Node): boolean {
    return node.type === 'ConditionalExpression';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const conditionalNode = node as any;
      
      // 检查是否为简单的三元表达式，可能豁免复杂度
      if (this.isSimpleTernary(conditionalNode)) {
        return this.createExemptionResult(
          node,
          '简单的三元运算符豁免复杂度',
          { exemptionType: 'simple-ternary' }
        );
      }
      
      // 基础复杂度：每个三元运算符增加 1 点复杂度
      const baseComplexity = 1;
      
      // 检查嵌套的三元运算符
      const nestingInfo = this.analyzeNestedTernary(conditionalNode);
      const additionalComplexity = nestingInfo.nestedCount;
      
      // 总基础复杂度
      const totalBaseComplexity = baseComplexity + additionalComplexity;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(totalBaseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateConditionalExpressionReason(
        totalBaseComplexity, 
        finalComplexity, 
        context, 
        nestingInfo
      );
      
      // 生成建议
      const suggestions = this.generateConditionalExpressionSuggestions(conditionalNode, nestingInfo, finalComplexity);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        false, // 三元运算符不增加嵌套层级
        suggestions,
        {
          baseComplexity: totalBaseComplexity,
          nestingLevel: context.nestingLevel,
          isNested: nestingInfo.isNested,
          nestedCount: nestingInfo.nestedCount,
          maxDepth: nestingInfo.maxDepth
        }
      );
    });
  }

  /**
   * 检查是否为简单的三元表达式
   * @param conditionalNode 条件表达式节点
   * @returns 是否为简单的三元表达式
   */
  private isSimpleTernary(conditionalNode: any): boolean {
    // 简单的三元表达式特征：
    // 1. 条件是简单的标识符或简单的比较
    // 2. 结果是简单的字面量或标识符
    // 3. 没有嵌套的三元运算符
    
    const test = conditionalNode.test;
    const consequent = conditionalNode.consequent;
    const alternate = conditionalNode.alternate;
    
    // 检查条件是否简单
    const isSimpleTest = test && (
      test.type === 'Identifier' ||
      test.type === 'BooleanLiteral' ||
      (test.type === 'BinaryExpression' && this.isSimpleComparison(test))
    );
    
    // 检查结果是否简单
    const isSimpleConsequent = consequent && this.isSimpleValue(consequent);
    const isSimpleAlternate = alternate && this.isSimpleValue(alternate);
    
    return isSimpleTest && isSimpleConsequent && isSimpleAlternate;
  }

  /**
   * 检查是否为简单的比较表达式
   * @param binaryNode 二元表达式节点
   * @returns 是否为简单比较
   */
  private isSimpleComparison(binaryNode: any): boolean {
    const comparisonOperators = ['==', '===', '!=', '!==', '<', '>', '<=', '>='];
    return comparisonOperators.includes(binaryNode.operator) &&
           this.isSimpleValue(binaryNode.left) &&
           this.isSimpleValue(binaryNode.right);
  }

  /**
   * 检查是否为简单值
   * @param node 节点
   * @returns 是否为简单值
   */
  private isSimpleValue(node: any): boolean {
    const simpleTypes = [
      'Identifier',
      'StringLiteral',
      'NumericLiteral',
      'BooleanLiteral',
      'NullLiteral',
      'UndefinedLiteral'
    ];
    return node && simpleTypes.includes(node.type);
  }

  /**
   * 分析嵌套的三元运算符
   * @param conditionalNode 条件表达式节点
   * @returns 嵌套分析结果
   */
  private analyzeNestedTernary(conditionalNode: any): {
    isNested: boolean;
    nestedCount: number;
    maxDepth: number;
  } {
    let nestedCount = 0;
    let maxDepth = 0;

    const analyzeNode = (node: any, depth: number = 0): void => {
      if (!node) return;
      
      maxDepth = Math.max(maxDepth, depth);
      
      if (node.type === 'ConditionalExpression') {
        if (depth > 0) {
          nestedCount++;
        }
        
        // 递归分析子节点
        analyzeNode(node.test, depth + 1);
        analyzeNode(node.consequent, depth + 1);
        analyzeNode(node.alternate, depth + 1);
      }
    };

    // 分析 consequent 和 alternate 中的嵌套三元运算符
    analyzeNode(conditionalNode.consequent, 1);
    analyzeNode(conditionalNode.alternate, 1);

    return {
      isNested: nestedCount > 0,
      nestedCount,
      maxDepth
    };
  }

  /**
   * 生成条件表达式的原因说明
   * @param baseComplexity 基础复杂度
   * @param finalComplexity 最终复杂度
   * @param context 分析上下文
   * @param nestingInfo 嵌套信息
   * @returns 原因说明
   */
  private generateConditionalExpressionReason(
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext,
    nestingInfo: { isNested: boolean; nestedCount: number }
  ): string {
    let reason = `三元运算符增加 ${baseComplexity} 点复杂度`;
    
    if (nestingInfo.isNested) {
      reason += ` (包含 ${nestingInfo.nestedCount} 个嵌套三元运算符)`;
    }
    
    if (context.nestingLevel > 0) {
      const nestingPenalty = finalComplexity - baseComplexity;
      if (nestingPenalty > 0) {
        reason += ` + ${nestingPenalty} 点嵌套惩罚 (嵌套层级: ${context.nestingLevel})`;
      }
    }
    
    return reason;
  }

  /**
   * 生成条件表达式的建议
   * @param conditionalNode 条件表达式节点
   * @param nestingInfo 嵌套信息
   * @param complexity 复杂度
   * @returns 建议数组
   */
  private generateConditionalExpressionSuggestions(
    conditionalNode: any,
    nestingInfo: { isNested: boolean; nestedCount: number; maxDepth: number },
    complexity: number
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    // 检查嵌套深度
    if (nestingInfo.isNested) {
      if (nestingInfo.maxDepth > 2) {
        suggestions.push({
          type: 'refactor',
          message: '嵌套的三元运算符难以理解，考虑使用 if-else 语句或提取为函数',
          priority: 'high'
        });
      } else {
        suggestions.push({
          type: 'refactor',
          message: '考虑将嵌套的三元运算符重构为更清晰的 if-else 语句',
          priority: 'medium'
        });
      }
    }
    
    // 检查复杂度
    if (complexity > 2) {
      suggestions.push({
        type: 'refactor',
        message: '复杂的三元运算符可能影响代码可读性，考虑使用 if-else 语句',
        priority: 'medium'
      });
    }
    
    // 检查条件复杂性
    if (this.hasComplexCondition(conditionalNode)) {
      suggestions.push({
        type: 'refactor',
        message: '三元运算符的条件较复杂，考虑提取为有意义的变量',
        priority: 'medium'
      });
    }
    
    return suggestions;
  }

  /**
   * 检查是否有复杂的条件
   * @param conditionalNode 条件表达式节点
   * @returns 是否有复杂条件
   */
  private hasComplexCondition(conditionalNode: any): boolean {
    const test = conditionalNode.test;
    if (!test) return false;
    
    // 复杂条件的特征
    const complexTypes = [
      'CallExpression',
      'LogicalExpression',
      'ConditionalExpression',
      'MemberExpression'
    ];
    
    return complexTypes.includes(test.type);
  }
}

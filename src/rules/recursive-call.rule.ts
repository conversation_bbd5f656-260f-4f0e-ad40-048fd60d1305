import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * RecursiveCallRule - 递归调用复杂度规则
 * 
 * 处理递归函数调用的认知复杂度计算：
 * - 每个递归调用增加 1 点复杂度
 * - 不增加嵌套层级（递归调用本身不创建新的控制流）
 * - 检测直接递归和间接递归
 * 
 * 支持的节点类型：
 * - CallExpression
 */
export class RecursiveCallRule extends BaseRule {
  readonly id = 'recursive-call';
  readonly name = 'Recursive Call Rule';
  readonly priority = 85; // 中等优先级

  canHandle(node: Node): boolean {
    return node.type === 'CallExpression';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const callNode = node as any;
      
      // 检查是否为递归调用
      const recursionInfo = this.analyzeRecursion(callNode, context);
      
      if (!recursionInfo.isRecursive) {
        // 不是递归调用，不增加复杂度
        return this.createNonExemptionResult(
          node,
          '非递归函数调用，不增加复杂度',
          { callType: 'non-recursive' }
        );
      }
      
      // 基础复杂度：每个递归调用增加 1 点复杂度
      const baseComplexity = 1;
      
      // 递归调用不应用嵌套惩罚，但仍然受当前嵌套层级影响
      const finalComplexity = baseComplexity;
      
      // 生成原因说明
      const reason = this.generateRecursiveCallReason(recursionInfo, finalComplexity);
      
      // 生成建议
      const suggestions = this.generateRecursiveCallSuggestions(callNode, recursionInfo, finalComplexity);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        false, // 递归调用不增加嵌套层级
        suggestions,
        {
          recursionType: recursionInfo.type,
          functionName: recursionInfo.functionName,
          nestingLevel: context.nestingLevel,
          isDirectRecursion: recursionInfo.type === 'direct',
          calleeType: callNode.callee?.type
        }
      );
    });
  }

  /**
   * 分析递归调用
   * @param callNode 函数调用节点
   * @param context 分析上下文
   * @returns 递归分析结果
   */
  private analyzeRecursion(callNode: any, context: AnalysisContext): {
    isRecursive: boolean;
    type: 'direct' | 'indirect' | 'none';
    functionName: string | null;
  } {
    const callee = callNode.callee;
    if (!callee) {
      return { isRecursive: false, type: 'none', functionName: null };
    }
    
    // 获取被调用函数的名称
    const calledFunctionName = this.extractFunctionName(callee);
    if (!calledFunctionName) {
      return { isRecursive: false, type: 'none', functionName: null };
    }
    
    // 获取当前函数名称（从上下文中）
    const currentFunctionName = this.getCurrentFunctionName(context);
    if (!currentFunctionName) {
      return { isRecursive: false, type: 'none', functionName: calledFunctionName };
    }
    
    // 检查直接递归
    if (calledFunctionName === currentFunctionName) {
      return { 
        isRecursive: true, 
        type: 'direct', 
        functionName: calledFunctionName 
      };
    }
    
    // TODO: 检查间接递归（需要更复杂的分析）
    // 目前只检测直接递归
    
    return { isRecursive: false, type: 'none', functionName: calledFunctionName };
  }

  /**
   * 提取函数名称
   * @param callee 被调用者节点
   * @returns 函数名称
   */
  private extractFunctionName(callee: any): string | null {
    switch (callee.type) {
      case 'Identifier':
        return callee.value || callee.name;
      
      case 'MemberExpression':
        // 对于 obj.method() 形式的调用
        if (callee.property && callee.property.type === 'Identifier') {
          return callee.property.value || callee.property.name;
        }
        break;
      
      case 'Super':
        return 'super';
      
      default:
        return null;
    }
    
    return null;
  }

  /**
   * 获取当前函数名称
   * @param context 分析上下文
   * @returns 当前函数名称
   */
  private getCurrentFunctionName(context: AnalysisContext): string | null {
    // 从上下文中获取当前函数名称
    // 这需要在 ComplexityVisitor 中设置
    return (context as any).currentFunctionName || null;
  }

  /**
   * 生成递归调用的原因说明
   * @param recursionInfo 递归信息
   * @param complexity 复杂度
   * @returns 原因说明
   */
  private generateRecursiveCallReason(
    recursionInfo: { type: string; functionName: string | null },
    complexity: number
  ): string {
    const functionName = recursionInfo.functionName || '未知函数';
    const recursionType = recursionInfo.type === 'direct' ? '直接' : '间接';
    
    return `${recursionType}递归调用 ${functionName} 增加 ${complexity} 点复杂度`;
  }

  /**
   * 生成递归调用的建议
   * @param callNode 函数调用节点
   * @param recursionInfo 递归信息
   * @param complexity 复杂度
   * @returns 建议数组
   */
  private generateRecursiveCallSuggestions(
    callNode: any,
    recursionInfo: { type: string; functionName: string | null },
    complexity: number
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    if (recursionInfo.type === 'direct') {
      suggestions.push({
        type: 'info',
        message: '确保递归函数有明确的终止条件以避免无限递归',
        priority: 'high'
      });
      
      suggestions.push({
        type: 'refactor',
        message: '考虑是否可以使用迭代方式替代递归以提高性能',
        priority: 'medium'
      });
    }
    
    // 检查是否在循环中进行递归调用
    if (this.isInLoop(callNode)) {
      suggestions.push({
        type: 'warning',
        message: '在循环中进行递归调用可能导致性能问题，请仔细检查',
        priority: 'high'
      });
    }
    
    return suggestions;
  }

  /**
   * 检查节点是否在循环中
   * @param node 节点
   * @returns 是否在循环中
   */
  private isInLoop(node: any): boolean {
    // 简化版本：这里需要访问父节点信息
    // 在实际实现中，可以通过上下文或父节点栈来判断
    return false;
  }
}

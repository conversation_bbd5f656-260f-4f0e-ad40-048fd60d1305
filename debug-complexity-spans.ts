import { ComplexityCalculator } from './src/core/calculator';

const testSourceCode = `export default function ComplexComponent() {
  if (someCondition) {
    console.log('test1');
  }
  
  for (let i = 0; i < 10; i++) {
    console.log('loop', i);
  }
  
  while (condition) {
    break;
  }
  
  return null;
}`;

console.log('测试复杂度计算器的span验证逻辑:');

async function debugSpanValidation() {
  try {
    const results = await ComplexityCalculator.analyze(testSourceCode, 'test.tsx');
    
    console.log('\n分析结果:');
    console.log('结果对象:', results);
    console.log('结果类型:', typeof results);
    console.log('结果键:', Object.keys(results || {}));
    
    if (results && results.functionDetails) {
      console.log('函数详情数量:', results.functionDetails.length);
      
      results.functionDetails.forEach((func, index) => {
        console.log(`\n函数 ${index + 1}:`);
        console.log('函数名:', func.name);
        console.log('复杂度:', func.complexity);
        console.log('位置:', `行${func.line}, 列${func.column}`);
        console.log('详情步骤数量:', func.details.length);
        
        console.log('\n详情步骤位置信息:');
        func.details.forEach((step, stepIndex) => {
          console.log(`  步骤 ${stepIndex + 1}:`);
          console.log(`    规则: ${step.ruleId}`);
          console.log(`    位置: 行${step.line}, 列${step.column}`);
          console.log(`    复杂度增量: ${step.increment}`);
          console.log(`    描述: ${step.description}`);
          
          // 如果有span信息，显示span
          if ((step as any).span) {
            console.log(`    span: ${(step as any).span.start}-${(step as any).span.end}`);
          }
        });
      });
    }
    
  } catch (error) {
    console.error('分析错误:', error);
  }
}

debugSpanValidation();
import { PositionConverter } from './src/utils/position-converter';

// 创建测试源代码
const testSourceCode = `/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

function testFunction() {
  if (true) {
    console.log('test');
    for (let i = 0; i < 10; i++) {
      console.log(i);
    }
  }
}

const anotherFunction = () => {
  while (true) {
    break;
  }
}`;

console.log('测试 PositionConverter.spanToPosition 方法:');
console.log('源代码长度:', testSourceCode.length);
console.log('源代码行数:', testSourceCode.split('\n').length);

// 测试不同的span位置
const testSpans = [
  0,    // 文件开头
  10,   // 第一行内某个位置
  50,   // 第二行左右
  100,  // import语句附近
  200,  // function声明附近
  300,  // if语句附近
  400,  // for循环附近
  500,  // 箭头函数附近
];

console.log('\n测试结果:');
testSpans.forEach(span => {
  try {
    const position = PositionConverter.spanToPosition(testSourceCode, span);
    const lineContent = PositionConverter.getLineContent(testSourceCode, position.line);
    console.log(`span ${span} -> 行${position.line}:${position.column} | ${lineContent.trim().substring(0, 50)}...`);
  } catch (error) {
    console.log(`span ${span} -> 错误: ${error}`);
  }
});

// 测试智能位置转换
console.log('\n测试智能位置转换:');
testSpans.forEach(span => {
  try {
    const position = PositionConverter.spanToPositionWithSmartFallback(testSourceCode, span);
    const lineContent = PositionConverter.getLineContent(testSourceCode, position.line);
    console.log(`智能转换 span ${span} -> 行${position.line}:${position.column} | ${lineContent.trim().substring(0, 50)}...`);
  } catch (error) {
    console.log(`智能转换 span ${span} -> 错误: ${error}`);
  }
});
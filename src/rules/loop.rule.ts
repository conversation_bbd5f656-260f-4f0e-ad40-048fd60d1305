import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * LoopRule - 循环语句复杂度规则
 * 
 * 处理所有类型循环语句的认知复杂度计算：
 * - 每个循环语句增加 1 点基础复杂度
 * - 应用嵌套层级惩罚
 * - 增加嵌套层级（影响子节点的复杂度计算）
 * 
 * 支持的节点类型：
 * - ForStatement
 * - ForInStatement
 * - ForOfStatement
 * - WhileStatement
 * - DoWhileStatement
 */
export class LoopRule extends BaseRule {
  readonly id = 'loop';
  readonly name = 'Loop Rule';
  readonly priority = 100; // 高优先级，基础控制流规则

  canHandle(node: Node): boolean {
    const loopTypes = [
      'ForStatement',
      'ForInStatement', 
      'ForOfStatement',
      'WhileStatement',
      'DoWhileStatement'
    ];
    return loopTypes.includes(node.type);
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 基础复杂度：每个循环语句都增加 1 点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateLoopReason(node.type, baseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateSuggestions(`${node.type.toLowerCase()} loop`, finalComplexity);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // 循环语句会增加嵌套层级
        suggestions,
        {
          baseComplexity,
          nestingLevel: context.nestingLevel,
          loopType: node.type,
          isInfiniteLoop: this.isInfiniteLoop(node as any)
        }
      );
    });
  }

  /**
   * 检查是否可能是无限循环
   * @param loopNode 循环节点
   * @returns 是否可能是无限循环
   */
  private isInfiniteLoop(loopNode: any): boolean {
    switch (loopNode.type) {
      case 'WhileStatement':
        // 检查条件是否为 true 字面量
        return loopNode.test && 
               loopNode.test.type === 'BooleanLiteral' && 
               loopNode.test.value === true;
      
      case 'ForStatement':
        // 检查是否没有条件（for(;;)）
        return !loopNode.test;
      
      default:
        return false;
    }
  }

  /**
   * 生成循环语句的原因说明
   * @param nodeType 节点类型
   * @param baseComplexity 基础复杂度
   * @param finalComplexity 最终复杂度
   * @param context 分析上下文
   * @returns 原因说明
   */
  private generateLoopReason(
    nodeType: string,
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext
  ): string {
    const loopName = this.getLoopDisplayName(nodeType);
    
    if (context.nestingLevel === 0) {
      return `${loopName}增加 ${baseComplexity} 点复杂度`;
    } else {
      const nestingPenalty = finalComplexity - baseComplexity;
      return `${loopName}增加 ${baseComplexity} 点基础复杂度 + ${nestingPenalty} 点嵌套惩罚 (嵌套层级: ${context.nestingLevel})`;
    }
  }

  /**
   * 获取循环类型的显示名称
   * @param nodeType 节点类型
   * @returns 显示名称
   */
  private getLoopDisplayName(nodeType: string): string {
    const nameMap: Record<string, string> = {
      'ForStatement': 'for 循环',
      'ForInStatement': 'for-in 循环',
      'ForOfStatement': 'for-of 循环',
      'WhileStatement': 'while 循环',
      'DoWhileStatement': 'do-while 循环'
    };
    return nameMap[nodeType] || '循环语句';
  }
}

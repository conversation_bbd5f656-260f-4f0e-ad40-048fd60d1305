/**
 * Token查找系统相关类型定义
 */

/**
 * Token查找策略枚举
 */
export enum TokenSearchStrategy {
  /** SWC AST Token 分析（最精确） */
  TOKEN_ANALYSIS = 'token-analysis',
  /** 智能字符串模式匹配（备用） */
  PATTERN_MATCHING = 'pattern-matching',
  /** 简单indexOf查找（兜底） */
  INDEX_FALLBACK = 'index-fallback',
  /** 所有策略都失败 */
  ALL_FAILED = 'all-failed'
}

/**
 * Token查找结果
 */
export interface TokenSearchResult {
  /** 找到的位置（字节偏移），null表示未找到 */
  position: number | null;
  /** 使用的查找策略 */
  strategy: TokenSearchStrategy;
  /** 搜索的关键字 */
  keyword: string;
  /** 搜索的节点类型 */
  nodeType: string;
  /** 是否成功找到 */
  success: boolean;
}

/**
 * Token查找配置
 */
export interface TokenSearchOptions {
  /** 是否启用详细日志 */
  enableLogging?: boolean;
  /** 搜索范围扩展大小（字节数） */
  rangeExpansion?: {
    /** 向前扩展 */
    before: number;
    /** 向后扩展 */
    after: number;
  };
  /** 是否使用所有降级策略 */
  useAllFallbacks?: boolean;
}

/**
 * 搜索范围定义
 */
export interface SearchRange {
  /** 起始字节偏移 */
  start: number;
  /** 结束字节偏移 */
  end: number;
}

// 核心类型定义
import type { Node } from '@swc/core';
export interface CalculationOptions {
  enableMixedLogicOperatorPenalty?: boolean;
  recursionChainThreshold?: number;
  enableDebugLog?: boolean; // 调试日志开关
  enableDetails?: boolean; // 详细日志模式开关
  quiet?: boolean; // 静默模式开关
}

export interface FunctionResult {
  name: string;
  complexity: number;
  line: number;
  column: number;
  filePath: string;
  severity?: 'Critical' | 'Warning' | 'Info';
  ignoreExemptions?: IgnoreExemption[];
  details?: DetailStep[]; // 详细计算步骤（仅在详细模式下填充）
}

export interface IgnoreExemption {
  line: number;
  type: 'ignore-next-line';
  complexityReduced: number;
}

export interface FileResult {
  filePath: string;
  complexity: number;
  functions: FunctionResult[];
  averageComplexity: number;
}

export interface AnalysisResult {
  summary: {
    totalComplexity: number;
    averageComplexity: number;
    filesAnalyzed: number;
    functionsAnalyzed: number;
    highComplexityFunctions: number;
  };
  results: FileResult[];
  baseline?: import('../baseline/types').BaselineData;
}

// 详细日志输出相关类型定义

/**
 * 规则分类枚举
 * 定义复杂度规则的不同类别
 */
export enum RuleCategory {
  /** 控制流规则 (if, switch, loops) */
  CONTROL_FLOW = 'control-flow',
  /** 逻辑操作符规则 (&&, ||) */
  LOGICAL_OPERATOR = 'logical-operator',
  /** 函数调用规则 */
  FUNCTION_CALL = 'function-call',
  /** 异常处理规则 (try-catch) */
  EXCEPTION_HANDLING = 'exception-handling',
  /** 递归规则 */
  RECURSION = 'recursion',
  /** 嵌套规则 */
  NESTING = 'nesting',
  /** JSX特定规则 */
  JSX = 'jsx',
  /** 其他规则 */
  OTHER = 'other'
}

/**
 * 规则注册配置类型
 * 用于批量注册规则时的配置
 */
export interface RuleRegistrationConfig {
  /** 规则列表 */
  rules: RuleMetadata[];
  /** 配置版本 */
  version: string;
  /** 配置描述 */
  description?: string;
  /** 是否覆盖已存在的规则 */
  overwrite?: boolean;
}

/**
 * 诊断标记类型
 * 用于标识计算过程中的异常情况
 */
export enum DiagnosticMarker {
  /** 正常情况，无标记 */
  NONE = '',
  /** 警告标记 - 异常计算行为 */
  WARNING = 'warning',
  /** 问号标记 - 未知规则或增量 */
  UNKNOWN = 'unknown',
  /** 错误标记 - 规则计算错误 */
  ERROR = 'error'
}

/**
 * 详细计算步骤数据结构
 * 表示单个复杂度增量的详细信息
 */
export interface DetailStep {
  /** 代码行号 */
  line: number;
  /** 代码列号 */
  column: number;
  /** 本步骤复杂度增量 */
  increment: number;
  /** 累计复杂度 */
  cumulative: number;
  /** 规则标识符 (kebab-case 格式) */
  ruleId: string;
  /** 人类可读的规则描述 */
  description: string;
  /** 嵌套层级 */
  nestingLevel: number;
  /** 可选的上下文信息 */
  context?: string;
  /** 诊断标记 */
  diagnosticMarker?: DiagnosticMarker;
  /** 诊断消息 */
  diagnosticMessage?: string;
  /** SWC span信息 (用于代码框架生成) */
  span?: { start: number; end: number };
  /** 是否应显示代码上下文 */
  shouldShowContext?: boolean;
  /** 上下文优先级排序 */
  contextRank?: number;
}

/**
 * 位置信息
 * 用于表示代码中的行列位置
 */
export interface Position {
  /** 行号 (从1开始) */
  line: number;
  /** 列号 (从0开始) */
  column: number;
}

/**
 * 代码框架生成选项
 * 用于配置@babel/code-frame的显示行为
 */
export interface CodeFrameOptions {
  /** 是否启用语法高亮 */
  highlightCode?: boolean;
  /** 显示目标行上方的行数 */
  linesAbove?: number;
  /** 显示目标行下方的行数 */
  linesBelow?: number;
  /** 是否强制使用颜色输出 */
  forceColor?: boolean;
}

/**
 * 代码框架生成结果
 * 包含生成的代码框架和相关元信息
 */
export interface CodeFrameResult {
  /** 生成的代码框架字符串 */
  frame: string;
  /** 是否生成成功 */
  success: boolean;
  /** 错误信息 (如果生成失败) */
  error?: string;
  /** 是否使用了缓存结果 */
  cached: boolean;
}

/**
 * 智能过滤选项
 * 用于控制上下文显示的过滤策略
 */
export interface FilterOptions {
  /** 最小复杂度增量阈值 */
  minComplexityIncrement: number;
  /** 最大上下文项目数量 */
  maxContextItems: number;
  /** 是否强制显示所有上下文 */
  forceShowAll: boolean;
}

/**
 * 详细格式化选项
 * 用于控制详细输出的格式化行为
 */
export interface DetailFormattingOptions {
  /** 是否显示代码上下文 */
  showContext: boolean;
  /** 代码框架生成选项 */
  contextOptions: CodeFrameOptions;
  /** 智能过滤选项 */
  filterOptions: FilterOptions;
}

/**
 * 函数详细信息
 * 包含函数级别的计算详情
 */
export interface FunctionDetail {
  /** 函数名称 */
  name: string;
  /** 函数起始行号 */
  line: number;
  /** 函数起始列号 */
  column: number;
  /** 最终复杂度 */
  complexity: number;
  /** 详细计算步骤 */
  details: DetailStep[];
}

/**
 * 规则元数据
 * 用于规则注册表的数据结构
 */
export interface RuleMetadata {
  /** 唯一规则标识符 (kebab-case) */
  ruleId: string;
  /** 规则描述 */
  description: string;
  /** 规则分类 */
  category: RuleCategory;
  /** 默认复杂度增量 */
  defaultIncrement: number;
  /** 是否启用 (可选，默认为true) */
  enabled?: boolean;
  /** 规则优先级 (可选，用于排序) */
  priority?: number;
}

// =============================================================================
// L1 层策略映射表类型定义
// =============================================================================

/**
 * 位置策略函数类型
 * 接收 AST 节点，返回 span 位置或 null（如果无法定位）
 */
export type PositionStrategy = (node: Node) => number | null;

/**
 * 策略上下文信息
 * 提供策略函数执行时需要的环境信息
 */
export interface StrategyContext {
  /** 源代码内容 */
  sourceCode: string;
  /** 父节点栈 */
  parentStack: Node[];
  /** 可选的 Token 解析器实例 */
  tokenizer?: any;
}

/**
 * 位置策略执行结果
 * 包含定位结果和元信息
 */
export interface PositionResult {
  /** 定位到的字节偏移位置，null 表示定位失败 */
  position: number | null;
  /** 定位置信度等级 */
  confidence: 'high' | 'medium' | 'low';
  /** 使用的定位方法描述 */
  method: string;
}

/**
 * 策略映射表条目
 * 定义每个节点类型对应的定位策略
 */
export interface PositionStrategyEntry {
  /** 节点类型 */
  nodeType: string;
  /** 主要定位策略函数 */
  strategy: PositionStrategy;
  /** 策略优先级（数字越小优先级越高） */
  priority: number;
  /** 可选的回退策略函数 */
  fallbackStrategy?: PositionStrategy;
}

/**
 * Token 搜索结果
 * SWC Token 流查找的结果信息
 */
export interface TokenSearchResult {
  /** 找到的位置，null 表示未找到 */
  position: number | null;
  /** 查找置信度 */
  confidence: 'high' | 'medium' | 'low';
  /** 使用的查找方法 */
  method: 'token-stream' | 'string-search' | 'fallback';
}

/**
 * Token 搜索范围
 * 限制 Token 查找的范围以优化性能
 */
export interface TokenSearchRange {
  /** 搜索起始位置 */
  start: number;
  /** 搜索结束位置 */
  end: number;
  /** 最大搜索 Token 数量 */
  maxTokens?: number;
}

/**
 * 错误恢复链
 * 定义策略失败时的多级回退机制
 */
export interface ErrorRecoveryChain {
  /** 主要策略函数 */
  primary: PositionStrategy;
  /** 回退策略函数数组（按优先级排序） */
  fallback: PositionStrategy[];
  /** 紧急情况下的保底位置生成器 */
  emergency: () => number;
}

/**
 * 错误恢复配置
 * 控制错误恢复机制的行为
 */
export interface ErrorRecoveryConfig {
  /** 是否启用错误恢复 */
  enabled: boolean;
  /** 最大回退尝试次数 */
  maxFallbackAttempts: number;
  /** 是否记录错误恢复过程 */
  logRecoveryProcess: boolean;
  /** 智能父节点选择 */
  intelligentParentSelection: boolean;
  /** 保底位置偏好 */
  defaultPositionPreferences: DefaultPositionPreferences;
}

/**
 * 保底位置偏好配置
 */
export interface DefaultPositionPreferences {
  /** 偏好使用函数开始位置 */
  preferFunctionStart: boolean;
  /** 偏好使用文件开始位置 */
  preferFileStart: boolean;
  /** 偏好使用上下文相关位置 */
  preferContextualPosition: boolean;
  /** 最小可接受行号 */
  minimumAcceptableLine: number;
}

/**
 * 错误恢复步骤记录
 * 记录错误恢复过程中的每个步骤
 */
export interface ErrorRecoveryStep {
  /** 步骤序号 */
  step: number;
  /** 尝试的策略类型 */
  strategyType: 'primary' | 'fallback' | 'parent' | 'emergency';
  /** 策略名称或描述 */
  strategyName: string;
  /** 是否成功 */
  success: boolean;
  /** 返回的位置 */
  position: number | null;
  /** 错误信息（如果失败） */
  error?: string;
  /** 执行时间（毫秒） */
  executionTime: number;
}

/**
 * 错误恢复结果
 * 完整的错误恢复过程结果
 */
export interface ErrorRecoveryResult {
  /** 最终成功的位置 */
  finalPosition: number;
  /** 成功的策略类型 */
  successfulStrategy: 'primary' | 'fallback' | 'parent' | 'emergency';
  /** 总尝试次数 */
  totalAttempts: number;
  /** 总执行时间 */
  totalExecutionTime: number;
  /** 所有恢复步骤 */
  recoverySteps: ErrorRecoveryStep[];
  /** 是否使用了紧急策略 */
  usedEmergencyStrategy: boolean;
}

/**
 * 错误分类枚举
 * 定义不同类型的错误级别
 */
export enum ErrorSeverity {
  /** 低级错误 - 轻微影响，有有效回退 */
  LOW = 'low',
  /** 中级错误 - 中等影响，需要多步回退 */
  MEDIUM = 'medium',
  /** 高级错误 - 严重影响，需要紧急回退 */
  HIGH = 'high',
  /** 严重错误 - 可能导致分析失败 */
  CRITICAL = 'critical'
}

/**
 * 智能父节点选择器配置
 */
export interface IntelligentParentSelectorConfig {
  /** 跳过的无意义节点类型 */
  skipNodeTypes: string[];
  /** 优先的父节点类型 */
  preferredNodeTypes: string[];
  /** 最大向上搜索层数 */
  maxAncestorLevels: number;
  /** 是否考虑span有效性 */
  considerSpanValidity: boolean;
}

/**
 * 保底位置生成器上下文
 */
export interface EmergencyPositionContext {
  /** 原始节点信息 */
  originalNode: {
    type: string;
    span?: { start: number; end: number };
  };
  /** 失败的策略列表 */
  failedStrategies: string[];
  /** 当前函数上下文 */
  functionContext?: {
    name: string;
    startPosition: number;
  };
  /** 源代码元信息 */
  sourceMetadata: {
    totalLines: number;
    totalLength: number;
    hasValidCode: boolean;
  };
}
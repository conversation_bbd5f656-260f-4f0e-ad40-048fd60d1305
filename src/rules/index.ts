/**
 * 规则集导出入口
 */

// 基础规则
export { BaseRule } from './base/rule';
export { BaseRule as BaseRuleClass } from './base-rule';

// 核心复杂度规则
export { IfStatementRule } from './if-statement.rule';
export { LoopRule } from './loop.rule';
export { LogicalOperatorRule } from './logical-operator.rule';
export { CatchClauseRule } from './catch-clause.rule';
export { RecursiveCallRule } from './recursive-call.rule';
export { SwitchStatementRule } from './switch-statement.rule';
export { ConditionalExpressionRule } from './conditional-expression.rule';

// JSX规则集
export * from './jsx';

// 其他独立规则
export { SmartConditionalRenderingRule } from './smart-conditional-rendering';
export { JSXEventHandlerRule } from './jsx-event-handler-rule';
export { JSXHookComplexityRule } from './jsx-hook-complexity';

// 规则集管理
export { RuleSetManager, JSXRuleSet, CoreRuleSet } from './rule-sets';

// 规则工厂函数
import type { Rule } from '../engine/types';
import { IfStatementRule } from './if-statement.rule';
import { LoopRule } from './loop.rule';
import { LogicalOperatorRule } from './logical-operator.rule';
import { CatchClauseRule } from './catch-clause.rule';
import { RecursiveCallRule } from './recursive-call.rule';
import { SwitchStatementRule } from './switch-statement.rule';
import { ConditionalExpressionRule } from './conditional-expression.rule';

/**
 * 创建所有默认核心规则实例
 * @returns 默认规则实例数组
 */
export function createDefaultCoreRules(): Rule[] {
  return [
    new IfStatementRule(),
    new LoopRule(),
    new LogicalOperatorRule(),
    new CatchClauseRule(),
    new RecursiveCallRule(),
    new SwitchStatementRule(),
    new ConditionalExpressionRule(),
  ];
}

/**
 * 核心规则类型映射
 */
export const CORE_RULE_CLASSES = {
  'if-statement': IfStatementRule,
  loop: LoopRule,
  'logical-operator': LogicalOperatorRule,
  'catch-clause': CatchClauseRule,
  'recursive-call': RecursiveCallRule,
  'switch-statement': SwitchStatementRule,
  'conditional-expression': ConditionalExpressionRule,
} as const;

/**
 * 根据规则ID创建核心规则实例
 * @param ruleId 规则ID
 * @returns 规则实例，如果不存在则返回 null
 */
export function createCoreRuleById(ruleId: keyof typeof CORE_RULE_CLASSES): Rule | null {
  const RuleClass = CORE_RULE_CLASSES[ruleId];
  return RuleClass ? new RuleClass() : null;
}

/**
 * 获取所有可用的核心规则ID
 * @returns 规则ID数组
 */
export function getAvailableCoreRuleIds(): string[] {
  return Object.keys(CORE_RULE_CLASSES);
}

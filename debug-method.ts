import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

const sourceCode = `
class TestClass {
  normalMethod() {
    return 'normal';
  }
}`;

console.log('Source code with positions:');
for (let i = 0; i < sourceCode.length; i++) {
  if (sourceCode.substring(i, i + 12) === 'normalMethod') {
    console.log(`'normalMethod' starts at position ${i}`);
  }
}

// 找到 'normalMethod' 的位置
const methodStart = sourceCode.indexOf('normalMethod');
const methodEnd = methodStart + 'normalMethod'.length;
console.log(`Method name range: ${methodStart} to ${methodEnd}`);

// 创建正确的模拟节点
const detailCollector = new DetailCollector();
const visitor = new ComplexityVisitor(sourceCode, detailCollector);

const mockNode = {
  type: 'MethodDefinition',
  span: { start: methodStart, end: sourceCode.indexOf('}', methodEnd + 20) },
  key: {
    type: 'Identifier',
    span: { start: methodStart, end: methodEnd },
    value: 'normalMethod'
  },
  kind: 'method'
};

console.log('\nMock node:', JSON.stringify(mockNode, null, 2));

const position = visitor.findMethodKeywordPosition(mockNode);
console.log('Found position:', position);

if (position !== null) {
  const foundMethod = sourceCode.slice(position, position + 12);
  console.log('Found method name:', foundMethod);
}
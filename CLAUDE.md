# AI 开发指南: cognitive-complexity CLI

- **描述**: Node.js CLI 工具 (使用 Bun 开发)
- **文件监控**: `_.ts, _.tsx, _.html, _.css, _.js, _.jsx, package.json`

## 核心策略

- **开发环境**: Bun (用于快速开发、测试、构建)
- **生产环境**: Node.js 18+ (代码必须 100% 兼容)
- **用户环境**: 通过 npm/npx 安装，在 Node.js 中运行

## CRITICAL: Node.js 兼容性规则

- **禁止**: 任何 `Bun.*` API (如 `Bun.file`, `Bun.serve`, `Bun.$`)。
- **必须**:
  - 使用 Node.js 原生 API (`fs`, `path`, `child_process`)。
  - 使用标准 npm 包 (`commander`, `cosmiconfig`, `@swc/core`)。
  - 构建产物为 Node.js 兼容的 ESM 模块。
  - 最终用户无需安装 Bun。

## 开发工作流与规则

- **向后兼容**: 开发新功能或重构时，无需考虑向后兼容。
- **类型检查**: 完成模块后，执行 `pnpm typecheck` 或 `pnpm typecheck -- /path/to/file`。
- **类型处理**: 优先修复类型根因，禁止使用 `as any`。
- **临时脚本**: 存放于 `@./temp/` 目录下。
- **禁止一切的 `as any`**: 如需使用，添加注释或询问用户是否允许

## 架构设计

- 最大可荣登单文件代码行数：1500 行，当代码量超过 1000 行时，需要思考下当下的架构应如何拆分模块，并告知用户
- **IoC 架构**: 项目已完成 IoC (Inversion of Control) 重构，采用依赖注入和工厂模式
- **核心组件**:
  - `CalculatorFactory`: 依赖注入工厂，支持配置驱动
  - `ComplexityCalculator`: 主分析器，支持实例和静态 API
  - `RuleEngine`: 规则引擎，支持可配置的并发和缓存
  - 资源管理: 自动清理，防止内存泄漏

## API 使用指南

### 静态 API (推荐用于单次分析)

```typescript
// 快速分析
const results = await ComplexityCalculator.analyze(code);

// 文件分析
const results = await ComplexityCalculator.analyzeFile('path/to/file.ts');

// 概览分析
const overview = await ComplexityCalculator.quickAnalyze(code);
```

### 实例 API (推荐用于批量处理)

```typescript
// 轻量级配置
const factory = createLightweightFactory();
const calculator = new ComplexityCalculator({}, factory);

// 完整功能配置
const factory = new CalculatorFactory({
  enableMonitoring: true,
  enableCaching: true,
  ruleEngineConfig: { maxRuleConcurrency: 8 },
});
const calculator = new ComplexityCalculator({}, factory);

// 使用后清理资源
try {
  const results = await calculator.calculateCode(code, 'file.ts');
} finally {
  await calculator.dispose();
}
```

## 命令

- `bun install`: 安装依赖
- `bun run dev`: 监听模式运行
- `bun run start`: 运行一次 CLI
- `bun run build`: 构建生产代码至 `dist/`
- `bun test`: 运行测试

## 项目结构

- `src/index.ts`: CLI 主入口
- `package.json`: 定义 CLI 二进制文件为 "complexity"
- `@/*`: TypeScript 路径别名，指向 `src/*`

## 测试

- **命令**: `bun test` (底层为 Vitest)
- **CI/CD**: 测试用例必须与 Node.js 测试运行器兼容。

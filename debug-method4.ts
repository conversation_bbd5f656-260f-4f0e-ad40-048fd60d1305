import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

const sourceCode = `
class TestClass {
  normalMethod() {
    return 'normal';
  }
}`;

class DebugComplexityVisitor extends ComplexityVisitor {
  public debugGetMethodSearchRange(node: any) {
    console.log('Node in getMethodSearchRange:', JSON.stringify(node, null, 2));
    
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 使用方法节点的 span 作为搜索范围
    if (node.span && typeof node.span.start === 'number' && typeof node.span.end === 'number') {
      console.log('Node has valid span');
      searchStart = node.span.start;
      // 限制搜索范围到方法名开始位置
      if (node.key && node.key.span && typeof node.key.span.start === 'number') {
        console.log('Node key has valid span');
        searchEnd = node.key.span.start + 50; // 给一些缓冲空间
        console.log(`Calculated searchEnd: ${node.key.span.start} + 50 = ${searchEnd}`);
      } else {
        console.log('Node key has no valid span, using default');
        searchEnd = node.span.start + 100; // 默认搜索前100个字符
      }
    } else {
      console.log('Node has no valid span');
    }

    console.log(`Search range: ${searchStart} to ${searchEnd}`);
    
    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      console.log('Search range is invalid!');
      console.log(`searchStart >= searchEnd: ${searchStart >= searchEnd}`);
      console.log(`searchStart < 0: ${searchStart < 0}`);
      console.log(`searchEnd > this.sourceCode.length: ${searchEnd > this.sourceCode.length} (${searchEnd} > ${this.sourceCode.length})`);
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }
}

const detailCollector = new DetailCollector();
const visitor = new DebugComplexityVisitor(sourceCode, detailCollector);

const methodStart = sourceCode.indexOf('normalMethod');
const methodEnd = methodStart + 'normalMethod'.length;

const mockNode = {
  type: 'MethodDefinition',
  span: { start: methodStart, end: sourceCode.indexOf('}', methodEnd + 20) },
  key: {
    type: 'Identifier',
    span: { start: methodStart, end: methodEnd },
    value: 'normalMethod'
  },
  kind: 'method'
};

console.log('Source code length:', sourceCode.length);
const searchRange = visitor.debugGetMethodSearchRange(mockNode);
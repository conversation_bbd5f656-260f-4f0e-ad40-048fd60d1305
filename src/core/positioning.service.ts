import type { Node } from '@swc/core';

/**
 * SWC 节点的 span 类型定义
 */
interface SwcSpan {
  start: number;
  end: number;
  ctxt: number;
}

/**
 * 具有 span 属性的节点类型
 */
interface NodeWithSpan extends Node {
  span: SwcSpan;
}

/**
 * PositioningService - 源代码位置计算和修正服务
 *
 * 专门负责处理 AST 节点的位置计算、span 修正和关键字定位。
 * 从 ComplexityVisitor 中剥离出来，实现关注点分离。
 *
 * 核心功能：
 * - span 验证和修正
 * - 关键字位置查找
 * - Token 搜索和定位
 * - 嵌套层级计算
 * - 行号转换
 */
export class PositioningService {
  /**
   * 源代码内容
   */
  private readonly sourceCode: string;

  /**
   * 构造函数
   * @param sourceCode 源代码内容
   */
  constructor(sourceCode: string) {
    this.sourceCode = sourceCode;
  }

  /**
   * Span 验证和修正系统
   *
   * 采用完善的多级错误恢复机制：
   * 1. 尝试主要位置策略（节点策略 → 原始span → 父节点策略）
   * 2. 智能父节点选择和回退机制
   * 3. 紧急位置生成（基于上下文的智能默认位置）
   *
   * @param node 要验证的 AST 节点
   * @returns 有效的字节偏移位置
   */
  public validateSpan(node: Node): number {
    try {
      // 尝试使用原始span
      if (this.isValidSpan(node)) {
        return (node as NodeWithSpan).span.start;
      }

      // 尝试父节点回退
      const correctedSpan = this.attemptParentSpanFallback(node);
      if (correctedSpan !== null) {
        return correctedSpan;
      }

      // 默认回退
      return this.getDefaultSpan();
    } catch {
      return 0;
    }
  }

  /**
   * 检查 span 是否有效
   * @param node 要检查的节点
   * @returns 是否有效
   */
  private isValidSpan(node: Node): node is NodeWithSpan {
    const nodeWithSpan = node as NodeWithSpan;
    return (
      nodeWithSpan.span &&
      typeof nodeWithSpan.span.start === 'number' &&
      typeof nodeWithSpan.span.end === 'number' &&
      nodeWithSpan.span.start >= 0 &&
      nodeWithSpan.span.end >= nodeWithSpan.span.start &&
      nodeWithSpan.span.start < this.sourceCode.length
    );
  }

  /**
   * 尝试使用父节点的 span 信息进行回退
   * @param node 当前节点
   * @returns 修正后的 span 位置，如果无法修正则返回 null
   */
  private attemptParentSpanFallback(_node: Node): number | null {
    // 简化版本的父节点回退逻辑
    // 在实际应用中，这里需要访问父节点信息
    // 由于我们无法直接访问父节点，返回 null
    return null;
  }

  /**
   * 获取默认 span 位置
   * 当所有修正策略都失败时使用
   * @returns 默认的字节偏移位置
   */
  private getDefaultSpan(): number {
    // 多级降级策略，确保总有有效位置返回

    // 第一级：尝试找到文件中第一个有效的代码位置
    const firstCodePosition = this.findFirstCodePosition();
    if (firstCodePosition !== null) {
      return firstCodePosition;
    }

    // 第二级：返回文件开始位置（绝对保底）
    return 0;
  }

  /**
   * 查找文件中第一个有效的代码位置
   * 跳过注释、空白行等
   * @returns 第一个代码位置，如果未找到则返回 null
   */
  private findFirstCodePosition(): number | null {
    const lines = this.sourceCode.split('\n');
    let offset = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // 跳过空行和注释行
      if (
        trimmedLine.length > 0 &&
        !trimmedLine.startsWith('//') &&
        !trimmedLine.startsWith('/*') &&
        !trimmedLine.startsWith('*')
      ) {
        // 找到第一个非空白字符的位置
        const firstNonSpace = line.search(/\S/);
        return firstNonSpace >= 0 ? offset + firstNonSpace : offset;
      }

      offset += line.length + 1; // +1 for newline character
    }

    return null;
  }

  /**
   * 在源代码中查找节点对应的关键字位置
   * 实例级的回退策略，可以访问源代码进行字符串搜索
   * @param node AST 节点
   * @returns 关键字位置，如果未找到则返回 null
   */
  public findKeywordPositionInSource(node: Node): number | null {
    // 优先使用专用的实例方法进行精确定位
    switch (node.type) {
      case 'ArrowFunctionExpression':
        return this.findArrowFunctionPosition(node);

      case 'JSXElement':
      case 'JSXFragment':
        return this.findJsxOpeningTagPosition(node);

      case 'ConditionalExpression':
        return this.findTernaryOperatorPosition(node);

      default:
        // 通用关键字查找逻辑
        const keyword = this.getKeywordForNodeType(node.type);
        if (keyword) {
          return this.findKeywordInRange(keyword, { start: 0, end: this.sourceCode.length });
        }
        return null;
    }
  }

  /**
   * 根据节点类型获取对应的关键字
   * @param nodeType 节点类型
   * @returns 关键字，如果没有则返回 null
   */
  private getKeywordForNodeType(nodeType: string): string | null {
    const keywordMap: Record<string, string> = {
      IfStatement: 'if',
      WhileStatement: 'while',
      DoWhileStatement: 'do',
      ForStatement: 'for',
      ForInStatement: 'for',
      ForOfStatement: 'for',
      SwitchStatement: 'switch',
      TryStatement: 'try',
      CatchClause: 'catch',
      ConditionalExpression: '?',
    };

    return keywordMap[nodeType] || null;
  }

  /**
   * 在指定范围内查找关键字
   * @param keyword 要查找的关键字
   * @param range 搜索范围
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findKeywordInRange(keyword: string, range: { start: number; end: number }): number | null {
    try {
      const searchText = this.sourceCode.slice(range.start, range.end);

      // 使用词边界正则表达式进行精确匹配
      const keywordRegex = new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g');
      const match = keywordRegex.exec(searchText);

      if (match) {
        return range.start + match.index;
      }

      return null;
    } catch (error) {
      console.debug(`Keyword search failed for '${keyword}':`, error);
      return null;
    }
  }

  /**
   * 转义正则表达式中的特殊字符
   * @param string 要转义的字符串
   * @returns 转义后的字符串
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 查找箭头函数位置
   * @param node 箭头函数节点
   * @returns 箭头函数的最佳位置
   */
  public findArrowFunctionPosition(node: any): number | null {
    if (!node || node.type !== 'ArrowFunctionExpression') {
      return null;
    }

    try {
      // 策略1：精确查找箭头符号 '=>'
      const arrowPosition = this.findArrowOperatorInSource(node);
      if (arrowPosition !== null) {
        return arrowPosition;
      }

      // 策略2：使用节点的 span 信息
      if (this.isValidSpan(node)) {
        return (node as NodeWithSpan).span.start;
      }

      return null;
    } catch (error) {
      console.debug('Arrow function position search failed:', error);
      return null;
    }
  }

  /**
   * 在源代码中查找箭头操作符位置
   * @param node 箭头函数节点
   * @returns 箭头操作符位置
   */
  private findArrowOperatorInSource(node: any): number | null {
    const searchRange = this.getSearchRange(node);
    if (!searchRange) {
      return null;
    }

    const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);

    // 查找箭头操作符
    const arrowRegex = /=>/g;
    const match = arrowRegex.exec(searchText);

    if (match) {
      return searchRange.start + match.index;
    }

    return null;
  }

  /**
   * 获取节点的搜索范围
   * @param node AST 节点
   * @returns 搜索范围
   */
  private getSearchRange(node: any): { start: number; end: number } | null {
    if (this.isValidSpan(node)) {
      return {
        start: node.span.start,
        end: node.span.end,
      };
    }

    // 如果没有有效的 span，使用整个源代码
    return {
      start: 0,
      end: this.sourceCode.length,
    };
  }

  /**
   * 查找 JSX 开放标签位置
   * @param node JSX 节点
   * @returns JSX 开放标签的字节偏移位置，如果未找到则返回 null
   */
  public findJsxOpeningTagPosition(node: any): number | null {
    try {
      // 验证节点类型
      if (!this.isJSXElementNode(node)) {
        return null;
      }

      // 获取搜索范围
      const searchRange = this.getSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 查找开放标签 '<'
      const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);
      const tagMatch = searchText.match(/<\w+|<>/); // 匹配 <tagName 或 <>

      if (tagMatch) {
        return searchRange.start + tagMatch.index!;
      }

      return null;
    } catch (error) {
      console.debug('JSX opening tag search failed:', error);
      return null;
    }
  }

  /**
   * 检查是否为 JSX 元素节点
   * @param node 节点
   * @returns 是否为 JSX 元素节点
   */
  private isJSXElementNode(node: any): boolean {
    return (
      node && (node.type === 'JSXElement' || node.type === 'JSXFragment' || node.type === 'JSXExpressionContainer')
    );
  }

  /**
   * 查找三元运算符位置
   * @param node 条件表达式节点
   * @returns 三元运算符位置
   */
  public findTernaryOperatorPosition(node: any): number | null {
    if (!node || node.type !== 'ConditionalExpression') {
      return null;
    }

    try {
      const searchRange = this.getSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 查找 '?' 符号
      const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);
      const questionMarkIndex = searchText.indexOf('?');

      if (questionMarkIndex >= 0) {
        return searchRange.start + questionMarkIndex;
      }

      return null;
    } catch (error) {
      console.debug('Ternary operator search failed:', error);
      return null;
    }
  }

  /**
   * 获取嵌套层级
   * 注意：这个方法需要访问访问者的状态，在实际使用时需要传入嵌套层级
   * @param nestingLevel 当前嵌套层级
   * @returns 嵌套层级
   */
  public getNestedLevel(nestingLevel: number): number {
    return nestingLevel;
  }

  /**
   * 获取行号
   * @param position 字节偏移位置
   * @returns 行号和列号
   */
  public getLineNumber(position: number): { line: number; column: number } {
    try {
      const lines = this.sourceCode.slice(0, position).split('\n');
      const line = lines.length;
      const column = lines[lines.length - 1]?.length || 0;
      return { line, column };
    } catch {
      return { line: 1, column: 0 };
    }
  }
}

import { describe, test, expect } from 'vitest';

describe('简单 JSX 测试', () => {
  test('基本 JSX 代码测试', async () => {
    // 导入模块
    const { ASTParser } = await import('../core/parser');
    
    const parser = new ASTParser();
    const code = `function MyComponent() { return <div>test</div>; }`;
    
    try {
      const ast = await parser.parseCode(code, 'test.tsx');
      console.log('解析成功，AST 类型:', ast.type);
      
      // 简单遍历找到 JSX 节点
      const findNodes = (node: any, types: string[] = []): string[] => {
        if (!node || typeof node !== 'object') return types;
        
        if (node.type && node.type.startsWith('JSX')) {
          types.push(node.type);
        }
        
        for (const key in node) {
          if (key === 'span' || key === 'type') continue;
          const value = node[key];
          
          if (Array.isArray(value)) {
            value.forEach(item => findNodes(item, types));
          } else if (value && typeof value === 'object') {
            findNodes(value, types);
          }
        }
        
        return types;
      };
      
      const jsxTypes = findNodes(ast);
      console.log('找到的 JSX 类型:', jsxTypes);
      
      expect(ast).toBeTruthy();
    } catch (error) {
      console.error('解析失败:', error);
      throw error;
    }
  });
});
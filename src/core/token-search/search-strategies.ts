/**
 * 搜索策略实现
 * 
 * 包含不同的Token搜索策略实现。
 * 从最精确的Token分析到简单的字符串查找。
 */

import type { TokenSearchRange } from '../types';

/**
 * 搜索策略类
 * 
 * 实现各种Token搜索策略。
 */
export class SearchStrategies {
  /**
   * 源代码内容
   */
  private readonly sourceCode: string;

  /**
   * 构造函数
   * 
   * @param sourceCode 源代码内容
   */
  constructor(sourceCode: string) {
    this.sourceCode = sourceCode;
  }

  /**
   * 策略1: 使用 SWC AST 和 Token 分析查找关键字
   * 
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  public findKeywordByTokenAnalysis(keyword: string, range: TokenSearchRange): number | null {
    try {
      // 由于 @swc/core v1.13.2 没有直接的 tokenize API，
      // 我们使用 AST 结合智能搜索的方式来实现"Token查找"效果
      
      // 提取搜索范围内的代码片段
      const searchCode = this.sourceCode.slice(range.start, range.end);
      
      // 使用正则表达式进行词边界匹配（模拟Token级别的精确性）
      const keywordRegex = new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g');
      let match;
      const candidates: number[] = [];
      
      while ((match = keywordRegex.exec(searchCode)) !== null) {
        const absolutePosition = range.start + match.index;
        candidates.push(absolutePosition);
      }
      
      // 如果找到候选位置，选择最合适的一个
      if (candidates.length > 0) {
        // 对于多个匹配，选择第一个（通常是节点开始处的关键字）
        const firstCandidate = candidates[0];
        return firstCandidate !== undefined ? firstCandidate : null;
      }
      
      return null;
    } catch (error) {
      console.debug('Token analysis failed:', error);
      return null;
    }
  }

  /**
   * 策略2: 智能字符串模式匹配
   * 
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  public findKeywordByPatternMatching(keyword: string, range: TokenSearchRange): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      
      // 创建多种可能的匹配模式
      const patterns = [
        // 标准模式：关键字后跟空白或括号
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\s*[\\(\\{\\s]`, 'g'),
        // 行首模式：行首的关键字
        new RegExp(`^\\s*${this.escapeRegExp(keyword)}\\b`, 'gm'),
        // 一般模式：词边界匹配
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g')
      ];
      
      for (const pattern of patterns) {
        pattern.lastIndex = 0; // 重置正则表达式状态
        const match = pattern.exec(searchCode);
        if (match) {
          // 找到匹配的关键字本身的位置（不包括后续的空白或括号）
          const keywordStart = match.index;
          const actualKeywordMatch = match[0].match(new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`));
          if (actualKeywordMatch) {
            const keywordOffset = match[0].indexOf(actualKeywordMatch[0]);
            return range.start + keywordStart + keywordOffset;
          }
        }
      }
      
      return null;
    } catch (error) {
      console.debug('Pattern matching failed:', error);
      return null;
    }
  }

  /**
   * 策略3: 简单 indexOf 查找（兜底策略）
   * 
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  public findKeywordByIndexOf(keyword: string, range: TokenSearchRange): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf(keyword);
      
      if (index >= 0) {
        return range.start + index;
      }
      
      return null;
    } catch (error) {
      console.debug('indexOf search failed:', error);
      return null;
    }
  }

  /**
   * 查找指定位置后的第一个非空白字符
   * 
   * @param position 起始位置
   * @param range 搜索范围
   * @returns 第一个非空白字符位置，如果未找到则返回 null
   */
  public findFirstNonWhitespaceAfter(position: number, range: TokenSearchRange): number | null {
    try {
      if (position < range.start || position >= range.end) {
        return null;
      }

      for (let i = position; i < range.end; i++) {
        const char = this.sourceCode[i];
        if (char && char.trim().length > 0) {
          return i;
        }
      }
      
      return null;
    } catch (error) {
      console.debug('First non-whitespace search failed:', error);
      return null;
    }
  }

  /**
   * 查找开放括号 '(' 的位置
   * 
   * @param range 搜索范围
   * @returns 括号位置，如果未找到则返回 null
   */
  public findOpeningParenthesis(range: TokenSearchRange): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf('(');
      
      if (index >= 0) {
        return range.start + index;
      }
      
      return null;
    } catch (error) {
      console.debug('Opening parenthesis search failed:', error);
      return null;
    }
  }

  /**
   * 查找开放大括号 '{' 的位置
   * 
   * @param range 搜索范围
   * @returns 大括号位置，如果未找到则返回 null
   */
  public findOpeningBrace(range: TokenSearchRange): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf('{');
      
      if (index >= 0) {
        return range.start + index;
      }
      
      return null;
    } catch (error) {
      console.debug('Opening brace search failed:', error);
      return null;
    }
  }

  /**
   * 查找开放尖括号 '<' 的位置
   * 
   * @param range 搜索范围
   * @returns 尖括号位置，如果未找到则返回 null
   */
  public findOpeningAngleBracket(range: TokenSearchRange): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf('<');
      
      if (index >= 0) {
        return range.start + index;
      }
      
      return null;
    } catch (error) {
      console.debug('Opening angle bracket search failed:', error);
      return null;
    }
  }

  /**
   * 查找冒号 ':' 的位置
   * 
   * @param range 搜索范围
   * @returns 冒号位置，如果未找到则返回 null
   */
  public findColon(range: TokenSearchRange): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      
      // 使用正则表达式查找 ':' 符号，避免误匹配对象属性
      const colonRegex = /\s*:\s*(?![:])/g; // 避免匹配 :: 等情况
      const match = colonRegex.exec(searchCode);
      
      if (match) {
        const colonIndex = match.index + match[0].indexOf(':');
        return range.start + colonIndex;
      }
      
      return null;
    } catch (error) {
      console.debug('Colon search failed:', error);
      return null;
    }
  }

  /**
   * 查找问号 '?' 的位置
   * 
   * @param range 搜索范围
   * @returns 问号位置，如果未找到则返回 null
   */
  public findQuestionMark(range: TokenSearchRange): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      
      // 使用正则表达式查找 '?' 符号，避免误匹配
      const questionRegex = /\s*\?\s*/g;
      const match = questionRegex.exec(searchCode);
      
      if (match) {
        const questionIndex = match.index + match[0].indexOf('?');
        return range.start + questionIndex;
      }
      
      return null;
    } catch (error) {
      console.debug('Question mark search failed:', error);
      return null;
    }
  }

  /**
   * 转义正则表达式中的特殊字符
   * 
   * @param string 要转义的字符串
   * @returns 转义后的字符串
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}

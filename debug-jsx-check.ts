import { ASTParser } from './src/core/parser';

async function main() {
  const parser = new ASTParser();
  
  // 测试不同类型的 JSX
  const testCases = [
    { name: '自闭合元素', code: `function MyComponent() { return <img src="test.jpg" />; }` },
    { name: '普通元素', code: `function MyComponent() { return <div>content</div>; }` },
    { name: 'Fragment', code: `function MyComponent() { return <>content</>; }` },
    { name: '表达式', code: `function MyComponent() { return <div>{value}</div>; }` }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n=== ${testCase.name} ===`);
    console.log('代码:', testCase.code);
    
    try {
      const ast = await parser.parseCode(testCase.code, 'test.tsx');
      
      const findJsxNodes = (node: any, path = ''): void => {
        if (!node || typeof node !== 'object') return;
        
        if (node.type) {
          if (node.type.startsWith('JSX')) {
            console.log(`找到 JSX 节点: ${path} → ${node.type}`);
            if (node.type === 'JSXElement') {
              console.log('  JSXElement 详情:', {
                type: node.type,
                hasOpening: !!node.opening,
                hasClosing: !!node.closing,
                hasChildren: !!node.children && node.children.length > 0,
                span: node.span
              });
              
              if (node.opening) {
                console.log('  Opening Element:', {
                  type: node.opening.type,
                  selfClosing: node.opening.selfClosing,
                  hasName: !!node.opening.name,
                  nameType: node.opening.name?.type,
                  nameValue: node.opening.name?.value,
                  span: node.opening.span
                });
              }
            }
          }
        }
        
        for (const key in node) {
          if (key === 'span' || key === 'type') continue;
          const value = node[key];
          
          if (Array.isArray(value)) {
            value.forEach((item, index) => findJsxNodes(item, `${path}.${key}[${index}]`));
          } else if (value && typeof value === 'object') {
            findJsxNodes(value, `${path}.${key}`);
          }
        }
      };
      
      findJsxNodes(ast, 'root');
      
    } catch (error) {
      console.error('解析失败:', error);
    }
  }
}

main();
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

const sourceCode = `
class TestClass {
  normalMethod() {
    return 'normal';
  }
}`;

const detailCollector = new DetailCollector();
const visitor = new ComplexityVisitor(sourceCode, detailCollector);

// 找到 'normalMethod' 的位置
const methodStart = sourceCode.indexOf('normalMethod');
const methodEnd = methodStart + 'normalMethod'.length;

const mockNode = {
  type: 'MethodDefinition',
  span: { start: methodStart, end: sourceCode.indexOf('}', methodEnd + 20) },
  key: {
    type: 'Identifier',
    span: { start: methodStart, end: methodEnd },
    value: 'normalMethod'
  },
  kind: 'method'
};

console.log('Source code:', sourceCode);
console.log('Mock node:', JSON.stringify(mockNode, null, 2));

// 手动测试搜索范围计算
const searchStart = mockNode.span.start; // 21
const searchEnd = mockNode.key.span.start + 50; // 21 + 50 = 71
console.log(`Search range: ${searchStart} to ${searchEnd}`);
console.log(`Search text: "${sourceCode.slice(searchStart, searchEnd)}"`);

// 手动测试关键字提取
const keywords = [];
if (mockNode.kind === 'method' && mockNode.key) {
  const methodName = mockNode.key.value || mockNode.key.name;
  if (methodName) {
    keywords.push(methodName);
  }
}
console.log('Keywords to search:', keywords);

// 手动测试正则匹配
const searchText = sourceCode.slice(searchStart, searchEnd);
const keyword = 'normalMethod';
const keywordRegex = new RegExp(`\\b${keyword}\\b`, 'g');
const match = keywordRegex.exec(searchText);
console.log('Regex match:', match);

if (match) {
  const foundPosition = searchStart + match.index;
  console.log('Found at position:', foundPosition);
  console.log('Found text:', sourceCode.slice(foundPosition, foundPosition + keyword.length));
}

// 测试实际方法
const position = visitor.findMethodKeywordPosition(mockNode);
console.log('Method result:', position);